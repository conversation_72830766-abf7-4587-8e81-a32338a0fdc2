"use strict";
(function (global, factory) {
    typeof define === 'function' && define.amd ? define(['exports'], factory) :
        typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
            (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.Parser = {}));
})(this, (function () {
    // Helper to replace import.meta.url
    function getCurrentScriptUrl() {
        if (typeof __filename !== 'undefined') {
            // Node.js environment
            return require('url').pathToFileURL(__filename).href;
        }
        if (typeof document !== 'undefined') {
            // Browser environment
            const script = document.currentScript;
            return script ? script.src : undefined;
        }
        throw new Error('Unable to determine script URL');
    }
    var __defProp = Object.defineProperty;
    var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
    var __require = /* @__PURE__ */ ((x) => typeof require !== "undefined" ? require : typeof Proxy !== "undefined" ? new Proxy(x, {
        get: (a, b) => (typeof require !== "undefined" ? require : a)[b]
    }) : x)(function (x) {
        if (typeof require !== "undefined")
            return require.apply(this, arguments);
        throw Error('Dynamic require of "' + x + '" is not supported');
    });
    // src/constants.ts
    var SIZE_OF_SHORT = 2;
    var SIZE_OF_INT = 4;
    var SIZE_OF_CURSOR = 4 * SIZE_OF_INT;
    var SIZE_OF_NODE = 5 * SIZE_OF_INT;
    var SIZE_OF_POINT = 2 * SIZE_OF_INT;
    var SIZE_OF_RANGE = 2 * SIZE_OF_INT + 2 * SIZE_OF_POINT;
    var ZERO_POINT = { row: 0, column: 0 };
    var INTERNAL = Symbol("INTERNAL");
    function assertInternal(x) {
        if (x !== INTERNAL)
            throw new Error("Illegal constructor");
    }
    __name(assertInternal, "assertInternal");
    function isPoint(point) {
        return !!point && typeof point.row === "number" && typeof point.column === "number";
    }
    __name(isPoint, "isPoint");
    function setModule(module2) {
        C = module2;
    }
    __name(setModule, "setModule");
    var C;
    // src/lookahead_iterator.ts
    var LookaheadIterator = class {
        static {
            __name(this, "LookaheadIterator");
        }
        /** @internal */
        constructor(internal, address, language) {
            /** @internal */
            this[0] = 0;
            assertInternal(internal);
            this[0] = address;
            this.language = language;
        }
        /** Get the current symbol of the lookahead iterator. */
        get currentTypeId() {
            return C._ts_lookahead_iterator_current_symbol(this[0]);
        }
        /** Get the current symbol name of the lookahead iterator. */
        get currentType() {
            return this.language.types[this.currentTypeId] || "ERROR";
        }
        /** Delete the lookahead iterator, freeing its resources. */
        delete() {
            C._ts_lookahead_iterator_delete(this[0]);
            this[0] = 0;
        }
        /**
         * Reset the lookahead iterator.
         *
         * This returns `true` if the language was set successfully and `false`
         * otherwise.
         */
        reset(language, stateId) {
            if (C._ts_lookahead_iterator_reset(this[0], language[0], stateId)) {
                this.language = language;
                return true;
            }
            return false;
        }
        /**
         * Reset the lookahead iterator to another state.
         *
         * This returns `true` if the iterator was reset to the given state and
         * `false` otherwise.
         */
        resetState(stateId) {
            return Boolean(C._ts_lookahead_iterator_reset_state(this[0], stateId));
        }
        /**
         * Returns an iterator that iterates over the symbols of the lookahead iterator.
         *
         * The iterator will yield the current symbol name as a string for each step
         * until there are no more symbols to iterate over.
         */
        [Symbol.iterator]() {
            return {
                next: /* @__PURE__ */ __name(() => {
                    if (C._ts_lookahead_iterator_next(this[0])) {
                        return { done: false, value: this.currentType };
                    }
                    return { done: true, value: "" };
                }, "next")
            };
        }
    };
    // src/tree.ts
    function getText(tree, startIndex, endIndex, startPosition) {
        const length = endIndex - startIndex;
        let result = tree.textCallback(startIndex, startPosition);
        if (result) {
            startIndex += result.length;
            while (startIndex < endIndex) {
                const string = tree.textCallback(startIndex, startPosition);
                if (string && string.length > 0) {
                    startIndex += string.length;
                    result += string;
                }
                else {
                    break;
                }
            }
            if (startIndex > endIndex) {
                result = result.slice(0, length);
            }
        }
        return result ?? "";
    }
    __name(getText, "getText");
    var Tree = class _Tree {
        static {
            __name(this, "Tree");
        }
        /** @internal */
        constructor(internal, address, language, textCallback) {
            /** @internal */
            this[0] = 0;
            assertInternal(internal);
            this[0] = address;
            this.language = language;
            this.textCallback = textCallback;
        }
        /** Create a shallow copy of the syntax tree. This is very fast. */
        copy() {
            const address = C._ts_tree_copy(this[0]);
            return new _Tree(INTERNAL, address, this.language, this.textCallback);
        }
        /** Delete the syntax tree, freeing its resources. */
        delete() {
            C._ts_tree_delete(this[0]);
            this[0] = 0;
        }
        /** Get the root node of the syntax tree. */
        get rootNode() {
            C._ts_tree_root_node_wasm(this[0]);
            return unmarshalNode(this);
        }
        /**
         * Get the root node of the syntax tree, but with its position shifted
         * forward by the given offset.
         */
        rootNodeWithOffset(offsetBytes, offsetExtent) {
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            C.setValue(address, offsetBytes, "i32");
            marshalPoint(address + SIZE_OF_INT, offsetExtent);
            C._ts_tree_root_node_with_offset_wasm(this[0]);
            return unmarshalNode(this);
        }
        /**
         * Edit the syntax tree to keep it in sync with source code that has been
         * edited.
         *
         * You must describe the edit both in terms of byte offsets and in terms of
         * row/column coordinates.
         */
        edit(edit) {
            marshalEdit(edit);
            C._ts_tree_edit_wasm(this[0]);
        }
        /** Create a new {@link TreeCursor} starting from the root of the tree. */
        walk() {
            return this.rootNode.walk();
        }
        /**
         * Compare this old edited syntax tree to a new syntax tree representing
         * the same document, returning a sequence of ranges whose syntactic
         * structure has changed.
         *
         * For this to work correctly, this syntax tree must have been edited such
         * that its ranges match up to the new tree. Generally, you'll want to
         * call this method right after calling one of the [`Parser::parse`]
         * functions. Call it on the old tree that was passed to parse, and
         * pass the new tree that was returned from `parse`.
         */
        getChangedRanges(other) {
            if (!(other instanceof _Tree)) {
                throw new TypeError("Argument must be a Tree");
            }
            C._ts_tree_get_changed_ranges_wasm(this[0], other[0]);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(count);
            if (count > 0) {
                let address = buffer;
                for (let i2 = 0; i2 < count; i2++) {
                    result[i2] = unmarshalRange(address);
                    address += SIZE_OF_RANGE;
                }
                C._free(buffer);
            }
            return result;
        }
        /** Get the included ranges that were used to parse the syntax tree. */
        getIncludedRanges() {
            C._ts_tree_included_ranges_wasm(this[0]);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(count);
            if (count > 0) {
                let address = buffer;
                for (let i2 = 0; i2 < count; i2++) {
                    result[i2] = unmarshalRange(address);
                    address += SIZE_OF_RANGE;
                }
                C._free(buffer);
            }
            return result;
        }
    };
    // src/tree_cursor.ts
    var TreeCursor = class _TreeCursor {
        static {
            __name(this, "TreeCursor");
        }
        /** @internal */
        constructor(internal, tree) {
            /** @internal */
            this[0] = 0;
            // Internal handle for WASM
            /** @internal */
            this[1] = 0;
            // Internal handle for WASM
            /** @internal */
            this[2] = 0;
            // Internal handle for WASM
            /** @internal */
            this[3] = 0;
            assertInternal(internal);
            this.tree = tree;
            unmarshalTreeCursor(this);
        }
        /** Creates a deep copy of the tree cursor. This allocates new memory. */
        copy() {
            const copy = new _TreeCursor(INTERNAL, this.tree);
            C._ts_tree_cursor_copy_wasm(this.tree[0]);
            unmarshalTreeCursor(copy);
            return copy;
        }
        /** Delete the tree cursor, freeing its resources. */
        delete() {
            marshalTreeCursor(this);
            C._ts_tree_cursor_delete_wasm(this.tree[0]);
            this[0] = this[1] = this[2] = 0;
        }
        /** Get the tree cursor's current {@link Node}. */
        get currentNode() {
            marshalTreeCursor(this);
            C._ts_tree_cursor_current_node_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /**
         * Get the numerical field id of this tree cursor's current node.
         *
         * See also {@link TreeCursor#currentFieldName}.
         */
        get currentFieldId() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_field_id_wasm(this.tree[0]);
        }
        /** Get the field name of this tree cursor's current node. */
        get currentFieldName() {
            return this.tree.language.fields[this.currentFieldId];
        }
        /**
         * Get the depth of the cursor's current node relative to the original
         * node that the cursor was constructed with.
         */
        get currentDepth() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_depth_wasm(this.tree[0]);
        }
        /**
         * Get the index of the cursor's current node out of all of the
         * descendants of the original node that the cursor was constructed with.
         */
        get currentDescendantIndex() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_descendant_index_wasm(this.tree[0]);
        }
        /** Get the type of the cursor's current node. */
        get nodeType() {
            return this.tree.language.types[this.nodeTypeId] || "ERROR";
        }
        /** Get the type id of the cursor's current node. */
        get nodeTypeId() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_node_type_id_wasm(this.tree[0]);
        }
        /** Get the state id of the cursor's current node. */
        get nodeStateId() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_node_state_id_wasm(this.tree[0]);
        }
        /** Get the id of the cursor's current node. */
        get nodeId() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_node_id_wasm(this.tree[0]);
        }
        /**
         * Check if the cursor's current node is *named*.
         *
         * Named nodes correspond to named rules in the grammar, whereas
         * *anonymous* nodes correspond to string literals in the grammar.
         */
        get nodeIsNamed() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_node_is_named_wasm(this.tree[0]) === 1;
        }
        /**
         * Check if the cursor's current node is *missing*.
         *
         * Missing nodes are inserted by the parser in order to recover from
         * certain kinds of syntax errors.
         */
        get nodeIsMissing() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_current_node_is_missing_wasm(this.tree[0]) === 1;
        }
        /** Get the string content of the cursor's current node. */
        get nodeText() {
            marshalTreeCursor(this);
            const startIndex = C._ts_tree_cursor_start_index_wasm(this.tree[0]);
            const endIndex = C._ts_tree_cursor_end_index_wasm(this.tree[0]);
            C._ts_tree_cursor_start_position_wasm(this.tree[0]);
            const startPosition = unmarshalPoint(TRANSFER_BUFFER);
            return getText(this.tree, startIndex, endIndex, startPosition);
        }
        /** Get the start position of the cursor's current node. */
        get startPosition() {
            marshalTreeCursor(this);
            C._ts_tree_cursor_start_position_wasm(this.tree[0]);
            return unmarshalPoint(TRANSFER_BUFFER);
        }
        /** Get the end position of the cursor's current node. */
        get endPosition() {
            marshalTreeCursor(this);
            C._ts_tree_cursor_end_position_wasm(this.tree[0]);
            return unmarshalPoint(TRANSFER_BUFFER);
        }
        /** Get the start index of the cursor's current node. */
        get startIndex() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_start_index_wasm(this.tree[0]);
        }
        /** Get the end index of the cursor's current node. */
        get endIndex() {
            marshalTreeCursor(this);
            return C._ts_tree_cursor_end_index_wasm(this.tree[0]);
        }
        /**
         * Move this cursor to the first child of its current node.
         *
         * This returns `true` if the cursor successfully moved, and returns
         * `false` if there were no children.
         */
        gotoFirstChild() {
            marshalTreeCursor(this);
            const result = C._ts_tree_cursor_goto_first_child_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Move this cursor to the last child of its current node.
         *
         * This returns `true` if the cursor successfully moved, and returns
         * `false` if there were no children.
         *
         * Note that this function may be slower than
         * {@link TreeCursor#gotoFirstChild} because it needs to
         * iterate through all the children to compute the child's position.
         */
        gotoLastChild() {
            marshalTreeCursor(this);
            const result = C._ts_tree_cursor_goto_last_child_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Move this cursor to the parent of its current node.
         *
         * This returns `true` if the cursor successfully moved, and returns
         * `false` if there was no parent node (the cursor was already on the
         * root node).
         *
         * Note that the node the cursor was constructed with is considered the root
         * of the cursor, and the cursor cannot walk outside this node.
         */
        gotoParent() {
            marshalTreeCursor(this);
            const result = C._ts_tree_cursor_goto_parent_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Move this cursor to the next sibling of its current node.
         *
         * This returns `true` if the cursor successfully moved, and returns
         * `false` if there was no next sibling node.
         *
         * Note that the node the cursor was constructed with is considered the root
         * of the cursor, and the cursor cannot walk outside this node.
         */
        gotoNextSibling() {
            marshalTreeCursor(this);
            const result = C._ts_tree_cursor_goto_next_sibling_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Move this cursor to the previous sibling of its current node.
         *
         * This returns `true` if the cursor successfully moved, and returns
         * `false` if there was no previous sibling node.
         *
         * Note that this function may be slower than
         * {@link TreeCursor#gotoNextSibling} due to how node
         * positions are stored. In the worst case, this will need to iterate
         * through all the children up to the previous sibling node to recalculate
         * its position. Also note that the node the cursor was constructed with is
         * considered the root of the cursor, and the cursor cannot walk outside this node.
         */
        gotoPreviousSibling() {
            marshalTreeCursor(this);
            const result = C._ts_tree_cursor_goto_previous_sibling_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Move the cursor to the node that is the nth descendant of
         * the original node that the cursor was constructed with, where
         * zero represents the original node itself.
         */
        gotoDescendant(goalDescendantIndex) {
            marshalTreeCursor(this);
            C._ts_tree_cursor_goto_descendant_wasm(this.tree[0], goalDescendantIndex);
            unmarshalTreeCursor(this);
        }
        /**
         * Move this cursor to the first child of its current node that contains or
         * starts after the given byte offset.
         *
         * This returns `true` if the cursor successfully moved to a child node, and returns
         * `false` if no such child was found.
         */
        gotoFirstChildForIndex(goalIndex) {
            marshalTreeCursor(this);
            C.setValue(TRANSFER_BUFFER + SIZE_OF_CURSOR, goalIndex, "i32");
            const result = C._ts_tree_cursor_goto_first_child_for_index_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Move this cursor to the first child of its current node that contains or
         * starts after the given byte offset.
         *
         * This returns the index of the child node if one was found, and returns
         * `null` if no such child was found.
         */
        gotoFirstChildForPosition(goalPosition) {
            marshalTreeCursor(this);
            marshalPoint(TRANSFER_BUFFER + SIZE_OF_CURSOR, goalPosition);
            const result = C._ts_tree_cursor_goto_first_child_for_position_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
            return result === 1;
        }
        /**
         * Re-initialize this tree cursor to start at the original node that the
         * cursor was constructed with.
         */
        reset(node) {
            marshalNode(node);
            marshalTreeCursor(this, TRANSFER_BUFFER + SIZE_OF_NODE);
            C._ts_tree_cursor_reset_wasm(this.tree[0]);
            unmarshalTreeCursor(this);
        }
        /**
         * Re-initialize a tree cursor to the same position as another cursor.
         *
         * Unlike {@link TreeCursor#reset}, this will not lose parent
         * information and allows reusing already created cursors.
         */
        resetTo(cursor) {
            marshalTreeCursor(this, TRANSFER_BUFFER);
            marshalTreeCursor(cursor, TRANSFER_BUFFER + SIZE_OF_CURSOR);
            C._ts_tree_cursor_reset_to_wasm(this.tree[0], cursor.tree[0]);
            unmarshalTreeCursor(this);
        }
    };
    // src/node.ts
    var Node = class {
        static {
            __name(this, "Node");
        }
        /** @internal */
        constructor(internal, { id, tree, startIndex, startPosition, other }) {
            /** @internal */
            this[0] = 0;
            assertInternal(internal);
            this[0] = other;
            this.id = id;
            this.tree = tree;
            this.startIndex = startIndex;
            this.startPosition = startPosition;
        }
        /** Get this node's type as a numerical id. */
        get typeId() {
            marshalNode(this);
            return C._ts_node_symbol_wasm(this.tree[0]);
        }
        /**
         * Get the node's type as a numerical id as it appears in the grammar,
         * ignoring aliases.
         */
        get grammarId() {
            marshalNode(this);
            return C._ts_node_grammar_symbol_wasm(this.tree[0]);
        }
        /** Get this node's type as a string. */
        get type() {
            return this.tree.language.types[this.typeId] || "ERROR";
        }
        /**
         * Get this node's symbol name as it appears in the grammar, ignoring
         * aliases as a string.
         */
        get grammarType() {
            return this.tree.language.types[this.grammarId] || "ERROR";
        }
        /**
         * Check if this node is *named*.
         *
         * Named nodes correspond to named rules in the grammar, whereas
         * *anonymous* nodes correspond to string literals in the grammar.
         */
        get isNamed() {
            marshalNode(this);
            return C._ts_node_is_named_wasm(this.tree[0]) === 1;
        }
        /**
         * Check if this node is *extra*.
         *
         * Extra nodes represent things like comments, which are not required
         * by the grammar, but can appear anywhere.
         */
        get isExtra() {
            marshalNode(this);
            return C._ts_node_is_extra_wasm(this.tree[0]) === 1;
        }
        /**
         * Check if this node represents a syntax error.
         *
         * Syntax errors represent parts of the code that could not be incorporated
         * into a valid syntax tree.
         */
        get isError() {
            marshalNode(this);
            return C._ts_node_is_error_wasm(this.tree[0]) === 1;
        }
        /**
         * Check if this node is *missing*.
         *
         * Missing nodes are inserted by the parser in order to recover from
         * certain kinds of syntax errors.
         */
        get isMissing() {
            marshalNode(this);
            return C._ts_node_is_missing_wasm(this.tree[0]) === 1;
        }
        /** Check if this node has been edited. */
        get hasChanges() {
            marshalNode(this);
            return C._ts_node_has_changes_wasm(this.tree[0]) === 1;
        }
        /**
         * Check if this node represents a syntax error or contains any syntax
         * errors anywhere within it.
         */
        get hasError() {
            marshalNode(this);
            return C._ts_node_has_error_wasm(this.tree[0]) === 1;
        }
        /** Get the byte index where this node ends. */
        get endIndex() {
            marshalNode(this);
            return C._ts_node_end_index_wasm(this.tree[0]);
        }
        /** Get the position where this node ends. */
        get endPosition() {
            marshalNode(this);
            C._ts_node_end_point_wasm(this.tree[0]);
            return unmarshalPoint(TRANSFER_BUFFER);
        }
        /** Get the string content of this node. */
        get text() {
            return getText(this.tree, this.startIndex, this.endIndex, this.startPosition);
        }
        /** Get this node's parse state. */
        get parseState() {
            marshalNode(this);
            return C._ts_node_parse_state_wasm(this.tree[0]);
        }
        /** Get the parse state after this node. */
        get nextParseState() {
            marshalNode(this);
            return C._ts_node_next_parse_state_wasm(this.tree[0]);
        }
        /** Check if this node is equal to another node. */
        equals(other) {
            return this.tree === other.tree && this.id === other.id;
        }
        /**
         * Get the node's child at the given index, where zero represents the first child.
         *
         * This method is fairly fast, but its cost is technically log(n), so if
         * you might be iterating over a long list of children, you should use
         * {@link Node#children} instead.
         */
        child(index) {
            marshalNode(this);
            C._ts_node_child_wasm(this.tree[0], index);
            return unmarshalNode(this.tree);
        }
        /**
         * Get this node's *named* child at the given index.
         *
         * See also {@link Node#isNamed}.
         * This method is fairly fast, but its cost is technically log(n), so if
         * you might be iterating over a long list of children, you should use
         * {@link Node#namedChildren} instead.
         */
        namedChild(index) {
            marshalNode(this);
            C._ts_node_named_child_wasm(this.tree[0], index);
            return unmarshalNode(this.tree);
        }
        /**
         * Get this node's child with the given numerical field id.
         *
         * See also {@link Node#childForFieldName}. You can
         * convert a field name to an id using {@link Language#fieldIdForName}.
         */
        childForFieldId(fieldId) {
            marshalNode(this);
            C._ts_node_child_by_field_id_wasm(this.tree[0], fieldId);
            return unmarshalNode(this.tree);
        }
        /**
         * Get the first child with the given field name.
         *
         * If multiple children may have the same field name, access them using
         * {@link Node#childrenForFieldName}.
         */
        childForFieldName(fieldName) {
            const fieldId = this.tree.language.fields.indexOf(fieldName);
            if (fieldId !== -1)
                return this.childForFieldId(fieldId);
            return null;
        }
        /** Get the field name of this node's child at the given index. */
        fieldNameForChild(index) {
            marshalNode(this);
            const address = C._ts_node_field_name_for_child_wasm(this.tree[0], index);
            if (!address)
                return null;
            return C.AsciiToString(address);
        }
        /** Get the field name of this node's named child at the given index. */
        fieldNameForNamedChild(index) {
            marshalNode(this);
            const address = C._ts_node_field_name_for_named_child_wasm(this.tree[0], index);
            if (!address)
                return null;
            return C.AsciiToString(address);
        }
        /**
         * Get an array of this node's children with a given field name.
         *
         * See also {@link Node#children}.
         */
        childrenForFieldName(fieldName) {
            const fieldId = this.tree.language.fields.indexOf(fieldName);
            if (fieldId !== -1 && fieldId !== 0)
                return this.childrenForFieldId(fieldId);
            return [];
        }
        /**
          * Get an array of this node's children with a given field id.
          *
          * See also {@link Node#childrenForFieldName}.
          */
        childrenForFieldId(fieldId) {
            marshalNode(this);
            C._ts_node_children_by_field_id_wasm(this.tree[0], fieldId);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(count);
            if (count > 0) {
                let address = buffer;
                for (let i2 = 0; i2 < count; i2++) {
                    result[i2] = unmarshalNode(this.tree, address);
                    address += SIZE_OF_NODE;
                }
                C._free(buffer);
            }
            return result;
        }
        /** Get the node's first child that contains or starts after the given byte offset. */
        firstChildForIndex(index) {
            marshalNode(this);
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            C.setValue(address, index, "i32");
            C._ts_node_first_child_for_byte_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get the node's first named child that contains or starts after the given byte offset. */
        firstNamedChildForIndex(index) {
            marshalNode(this);
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            C.setValue(address, index, "i32");
            C._ts_node_first_named_child_for_byte_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get this node's number of children. */
        get childCount() {
            marshalNode(this);
            return C._ts_node_child_count_wasm(this.tree[0]);
        }
        /**
         * Get this node's number of *named* children.
         *
         * See also {@link Node#isNamed}.
         */
        get namedChildCount() {
            marshalNode(this);
            return C._ts_node_named_child_count_wasm(this.tree[0]);
        }
        /** Get this node's first child. */
        get firstChild() {
            return this.child(0);
        }
        /**
         * Get this node's first named child.
         *
         * See also {@link Node#isNamed}.
         */
        get firstNamedChild() {
            return this.namedChild(0);
        }
        /** Get this node's last child. */
        get lastChild() {
            return this.child(this.childCount - 1);
        }
        /**
         * Get this node's last named child.
         *
         * See also {@link Node#isNamed}.
         */
        get lastNamedChild() {
            return this.namedChild(this.namedChildCount - 1);
        }
        /**
         * Iterate over this node's children.
         *
         * If you're walking the tree recursively, you may want to use the
         * {@link TreeCursor} APIs directly instead.
         */
        get children() {
            if (!this._children) {
                marshalNode(this);
                C._ts_node_children_wasm(this.tree[0]);
                const count = C.getValue(TRANSFER_BUFFER, "i32");
                const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
                this._children = new Array(count);
                if (count > 0) {
                    let address = buffer;
                    for (let i2 = 0; i2 < count; i2++) {
                        this._children[i2] = unmarshalNode(this.tree, address);
                        address += SIZE_OF_NODE;
                    }
                    C._free(buffer);
                }
            }
            return this._children;
        }
        /**
         * Iterate over this node's named children.
         *
         * See also {@link Node#children}.
         */
        get namedChildren() {
            if (!this._namedChildren) {
                marshalNode(this);
                C._ts_node_named_children_wasm(this.tree[0]);
                const count = C.getValue(TRANSFER_BUFFER, "i32");
                const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
                this._namedChildren = new Array(count);
                if (count > 0) {
                    let address = buffer;
                    for (let i2 = 0; i2 < count; i2++) {
                        this._namedChildren[i2] = unmarshalNode(this.tree, address);
                        address += SIZE_OF_NODE;
                    }
                    C._free(buffer);
                }
            }
            return this._namedChildren;
        }
        /**
         * Get the descendants of this node that are the given type, or in the given types array.
         *
         * The types array should contain node type strings, which can be retrieved from {@link Language#types}.
         *
         * Additionally, a `startPosition` and `endPosition` can be passed in to restrict the search to a byte range.
         */
        descendantsOfType(types, startPosition = ZERO_POINT, endPosition = ZERO_POINT) {
            if (!Array.isArray(types))
                types = [types];
            const symbols = [];
            const typesBySymbol = this.tree.language.types;
            for (let i2 = 0, n = typesBySymbol.length; i2 < n; i2++) {
                if (types.includes(typesBySymbol[i2])) {
                    symbols.push(i2);
                }
            }
            const symbolsAddress = C._malloc(SIZE_OF_INT * symbols.length);
            for (let i2 = 0, n = symbols.length; i2 < n; i2++) {
                C.setValue(symbolsAddress + i2 * SIZE_OF_INT, symbols[i2], "i32");
            }
            marshalNode(this);
            C._ts_node_descendants_of_type_wasm(this.tree[0], symbolsAddress, symbols.length, startPosition.row, startPosition.column, endPosition.row, endPosition.column);
            const descendantCount = C.getValue(TRANSFER_BUFFER, "i32");
            const descendantAddress = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(descendantCount);
            if (descendantCount > 0) {
                let address = descendantAddress;
                for (let i2 = 0; i2 < descendantCount; i2++) {
                    result[i2] = unmarshalNode(this.tree, address);
                    address += SIZE_OF_NODE;
                }
            }
            C._free(descendantAddress);
            C._free(symbolsAddress);
            return result;
        }
        /** Get this node's next sibling. */
        get nextSibling() {
            marshalNode(this);
            C._ts_node_next_sibling_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get this node's previous sibling. */
        get previousSibling() {
            marshalNode(this);
            C._ts_node_prev_sibling_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /**
         * Get this node's next *named* sibling.
         *
         * See also {@link Node#isNamed}.
         */
        get nextNamedSibling() {
            marshalNode(this);
            C._ts_node_next_named_sibling_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /**
         * Get this node's previous *named* sibling.
         *
         * See also {@link Node#isNamed}.
         */
        get previousNamedSibling() {
            marshalNode(this);
            C._ts_node_prev_named_sibling_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get the node's number of descendants, including one for the node itself. */
        get descendantCount() {
            marshalNode(this);
            return C._ts_node_descendant_count_wasm(this.tree[0]);
        }
        /**
         * Get this node's immediate parent.
         * Prefer {@link Node#childWithDescendant} for iterating over this node's ancestors.
         */
        get parent() {
            marshalNode(this);
            C._ts_node_parent_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /**
         * Get the node that contains `descendant`.
         *
         * Note that this can return `descendant` itself.
         */
        childWithDescendant(descendant) {
            marshalNode(this);
            marshalNode(descendant);
            C._ts_node_child_with_descendant_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get the smallest node within this node that spans the given byte range. */
        descendantForIndex(start2, end = start2) {
            if (typeof start2 !== "number" || typeof end !== "number") {
                throw new Error("Arguments must be numbers");
            }
            marshalNode(this);
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            C.setValue(address, start2, "i32");
            C.setValue(address + SIZE_OF_INT, end, "i32");
            C._ts_node_descendant_for_index_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get the smallest named node within this node that spans the given byte range. */
        namedDescendantForIndex(start2, end = start2) {
            if (typeof start2 !== "number" || typeof end !== "number") {
                throw new Error("Arguments must be numbers");
            }
            marshalNode(this);
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            C.setValue(address, start2, "i32");
            C.setValue(address + SIZE_OF_INT, end, "i32");
            C._ts_node_named_descendant_for_index_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get the smallest node within this node that spans the given point range. */
        descendantForPosition(start2, end = start2) {
            if (!isPoint(start2) || !isPoint(end)) {
                throw new Error("Arguments must be {row, column} objects");
            }
            marshalNode(this);
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            marshalPoint(address, start2);
            marshalPoint(address + SIZE_OF_POINT, end);
            C._ts_node_descendant_for_position_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /** Get the smallest named node within this node that spans the given point range. */
        namedDescendantForPosition(start2, end = start2) {
            if (!isPoint(start2) || !isPoint(end)) {
                throw new Error("Arguments must be {row, column} objects");
            }
            marshalNode(this);
            const address = TRANSFER_BUFFER + SIZE_OF_NODE;
            marshalPoint(address, start2);
            marshalPoint(address + SIZE_OF_POINT, end);
            C._ts_node_named_descendant_for_position_wasm(this.tree[0]);
            return unmarshalNode(this.tree);
        }
        /**
         * Create a new {@link TreeCursor} starting from this node.
         *
         * Note that the given node is considered the root of the cursor,
         * and the cursor cannot walk outside this node.
         */
        walk() {
            marshalNode(this);
            C._ts_tree_cursor_new_wasm(this.tree[0]);
            return new TreeCursor(INTERNAL, this.tree);
        }
        /**
         * Edit this node to keep it in-sync with source code that has been edited.
         *
         * This function is only rarely needed. When you edit a syntax tree with
         * the {@link Tree#edit} method, all of the nodes that you retrieve from
         * the tree afterward will already reflect the edit. You only need to
         * use {@link Node#edit} when you have a specific {@link Node} instance that
         * you want to keep and continue to use after an edit.
         */
        edit(edit) {
            if (this.startIndex >= edit.oldEndIndex) {
                this.startIndex = edit.newEndIndex + (this.startIndex - edit.oldEndIndex);
                let subbedPointRow;
                let subbedPointColumn;
                if (this.startPosition.row > edit.oldEndPosition.row) {
                    subbedPointRow = this.startPosition.row - edit.oldEndPosition.row;
                    subbedPointColumn = this.startPosition.column;
                }
                else {
                    subbedPointRow = 0;
                    subbedPointColumn = this.startPosition.column;
                    if (this.startPosition.column >= edit.oldEndPosition.column) {
                        subbedPointColumn = this.startPosition.column - edit.oldEndPosition.column;
                    }
                }
                if (subbedPointRow > 0) {
                    this.startPosition.row += subbedPointRow;
                    this.startPosition.column = subbedPointColumn;
                }
                else {
                    this.startPosition.column += subbedPointColumn;
                }
            }
            else if (this.startIndex > edit.startIndex) {
                this.startIndex = edit.newEndIndex;
                this.startPosition.row = edit.newEndPosition.row;
                this.startPosition.column = edit.newEndPosition.column;
            }
        }
        /** Get the S-expression representation of this node. */
        toString() {
            marshalNode(this);
            const address = C._ts_node_to_string_wasm(this.tree[0]);
            const result = C.AsciiToString(address);
            C._free(address);
            return result;
        }
    };
    // src/marshal.ts
    function unmarshalCaptures(query, tree, address, patternIndex, result) {
        for (let i2 = 0, n = result.length; i2 < n; i2++) {
            const captureIndex = C.getValue(address, "i32");
            address += SIZE_OF_INT;
            const node = unmarshalNode(tree, address);
            address += SIZE_OF_NODE;
            result[i2] = { patternIndex, name: query.captureNames[captureIndex], node };
        }
        return address;
    }
    __name(unmarshalCaptures, "unmarshalCaptures");
    function marshalNode(node) {
        let address = TRANSFER_BUFFER;
        C.setValue(address, node.id, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, node.startIndex, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, node.startPosition.row, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, node.startPosition.column, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, node[0], "i32");
    }
    __name(marshalNode, "marshalNode");
    function unmarshalNode(tree, address = TRANSFER_BUFFER) {
        const id = C.getValue(address, "i32");
        address += SIZE_OF_INT;
        if (id === 0)
            return null;
        const index = C.getValue(address, "i32");
        address += SIZE_OF_INT;
        const row = C.getValue(address, "i32");
        address += SIZE_OF_INT;
        const column = C.getValue(address, "i32");
        address += SIZE_OF_INT;
        const other = C.getValue(address, "i32");
        const result = new Node(INTERNAL, {
            id,
            tree,
            startIndex: index,
            startPosition: { row, column },
            other
        });
        return result;
    }
    __name(unmarshalNode, "unmarshalNode");
    function marshalTreeCursor(cursor, address = TRANSFER_BUFFER) {
        C.setValue(address + 0 * SIZE_OF_INT, cursor[0], "i32");
        C.setValue(address + 1 * SIZE_OF_INT, cursor[1], "i32");
        C.setValue(address + 2 * SIZE_OF_INT, cursor[2], "i32");
        C.setValue(address + 3 * SIZE_OF_INT, cursor[3], "i32");
    }
    __name(marshalTreeCursor, "marshalTreeCursor");
    function unmarshalTreeCursor(cursor) {
        cursor[0] = C.getValue(TRANSFER_BUFFER + 0 * SIZE_OF_INT, "i32");
        cursor[1] = C.getValue(TRANSFER_BUFFER + 1 * SIZE_OF_INT, "i32");
        cursor[2] = C.getValue(TRANSFER_BUFFER + 2 * SIZE_OF_INT, "i32");
        cursor[3] = C.getValue(TRANSFER_BUFFER + 3 * SIZE_OF_INT, "i32");
    }
    __name(unmarshalTreeCursor, "unmarshalTreeCursor");
    function marshalPoint(address, point) {
        C.setValue(address, point.row, "i32");
        C.setValue(address + SIZE_OF_INT, point.column, "i32");
    }
    __name(marshalPoint, "marshalPoint");
    function unmarshalPoint(address) {
        const result = {
            row: C.getValue(address, "i32") >>> 0,
            column: C.getValue(address + SIZE_OF_INT, "i32") >>> 0
        };
        return result;
    }
    __name(unmarshalPoint, "unmarshalPoint");
    function marshalRange(address, range) {
        marshalPoint(address, range.startPosition);
        address += SIZE_OF_POINT;
        marshalPoint(address, range.endPosition);
        address += SIZE_OF_POINT;
        C.setValue(address, range.startIndex, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, range.endIndex, "i32");
        address += SIZE_OF_INT;
    }
    __name(marshalRange, "marshalRange");
    function unmarshalRange(address) {
        const result = {};
        result.startPosition = unmarshalPoint(address);
        address += SIZE_OF_POINT;
        result.endPosition = unmarshalPoint(address);
        address += SIZE_OF_POINT;
        result.startIndex = C.getValue(address, "i32") >>> 0;
        address += SIZE_OF_INT;
        result.endIndex = C.getValue(address, "i32") >>> 0;
        return result;
    }
    __name(unmarshalRange, "unmarshalRange");
    function marshalEdit(edit, address = TRANSFER_BUFFER) {
        marshalPoint(address, edit.startPosition);
        address += SIZE_OF_POINT;
        marshalPoint(address, edit.oldEndPosition);
        address += SIZE_OF_POINT;
        marshalPoint(address, edit.newEndPosition);
        address += SIZE_OF_POINT;
        C.setValue(address, edit.startIndex, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, edit.oldEndIndex, "i32");
        address += SIZE_OF_INT;
        C.setValue(address, edit.newEndIndex, "i32");
        address += SIZE_OF_INT;
    }
    __name(marshalEdit, "marshalEdit");
    function unmarshalLanguageMetadata(address) {
        const result = {};
        result.major_version = C.getValue(address, "i32");
        address += SIZE_OF_INT;
        result.minor_version = C.getValue(address, "i32");
        address += SIZE_OF_INT;
        result.field_count = C.getValue(address, "i32");
        return result;
    }
    __name(unmarshalLanguageMetadata, "unmarshalLanguageMetadata");
    // src/query.ts
    var PREDICATE_STEP_TYPE_CAPTURE = 1;
    var PREDICATE_STEP_TYPE_STRING = 2;
    var QUERY_WORD_REGEX = /[\w-]+/g;
    var CaptureQuantifier = {
        Zero: 0,
        ZeroOrOne: 1,
        ZeroOrMore: 2,
        One: 3,
        OneOrMore: 4
    };
    var isCaptureStep = /* @__PURE__ */ __name((step) => step.type === "capture", "isCaptureStep");
    var isStringStep = /* @__PURE__ */ __name((step) => step.type === "string", "isStringStep");
    var QueryErrorKind = {
        Syntax: 1,
        NodeName: 2,
        FieldName: 3,
        CaptureName: 4,
        PatternStructure: 5
    };
    var QueryError = class _QueryError extends Error {
        constructor(kind, info2, index, length) {
            super(_QueryError.formatMessage(kind, info2));
            this.kind = kind;
            this.info = info2;
            this.index = index;
            this.length = length;
            this.name = "QueryError";
        }
        static {
            __name(this, "QueryError");
        }
        /** Formats an error message based on the error kind and info */
        static formatMessage(kind, info2) {
            switch (kind) {
                case QueryErrorKind.NodeName:
                    return `Bad node name '${info2.word}'`;
                case QueryErrorKind.FieldName:
                    return `Bad field name '${info2.word}'`;
                case QueryErrorKind.CaptureName:
                    return `Bad capture name @${info2.word}`;
                case QueryErrorKind.PatternStructure:
                    return `Bad pattern structure at offset ${info2.suffix}`;
                case QueryErrorKind.Syntax:
                    return `Bad syntax at offset ${info2.suffix}`;
            }
        }
    };
    function parseAnyPredicate(steps, index, operator, textPredicates) {
        if (steps.length !== 3) {
            throw new Error(`Wrong number of arguments to \`#${operator}\` predicate. Expected 2, got ${steps.length - 1}`);
        }
        if (!isCaptureStep(steps[1])) {
            throw new Error(`First argument of \`#${operator}\` predicate must be a capture. Got "${steps[1].value}"`);
        }
        const isPositive = operator === "eq?" || operator === "any-eq?";
        const matchAll = !operator.startsWith("any-");
        if (isCaptureStep(steps[2])) {
            const captureName1 = steps[1].name;
            const captureName2 = steps[2].name;
            textPredicates[index].push((captures) => {
                const nodes1 = [];
                const nodes2 = [];
                for (const c of captures) {
                    if (c.name === captureName1)
                        nodes1.push(c.node);
                    if (c.name === captureName2)
                        nodes2.push(c.node);
                }
                const compare = /* @__PURE__ */ __name((n1, n2, positive) => {
                    return positive ? n1.text === n2.text : n1.text !== n2.text;
                }, "compare");
                return matchAll ? nodes1.every((n1) => nodes2.some((n2) => compare(n1, n2, isPositive))) : nodes1.some((n1) => nodes2.some((n2) => compare(n1, n2, isPositive)));
            });
        }
        else {
            const captureName = steps[1].name;
            const stringValue = steps[2].value;
            const matches = /* @__PURE__ */ __name((n) => n.text === stringValue, "matches");
            const doesNotMatch = /* @__PURE__ */ __name((n) => n.text !== stringValue, "doesNotMatch");
            textPredicates[index].push((captures) => {
                const nodes = [];
                for (const c of captures) {
                    if (c.name === captureName)
                        nodes.push(c.node);
                }
                const test = isPositive ? matches : doesNotMatch;
                return matchAll ? nodes.every(test) : nodes.some(test);
            });
        }
    }
    __name(parseAnyPredicate, "parseAnyPredicate");
    function parseMatchPredicate(steps, index, operator, textPredicates) {
        if (steps.length !== 3) {
            throw new Error(`Wrong number of arguments to \`#${operator}\` predicate. Expected 2, got ${steps.length - 1}.`);
        }
        if (steps[1].type !== "capture") {
            throw new Error(`First argument of \`#${operator}\` predicate must be a capture. Got "${steps[1].value}".`);
        }
        if (steps[2].type !== "string") {
            throw new Error(`Second argument of \`#${operator}\` predicate must be a string. Got @${steps[2].name}.`);
        }
        const isPositive = operator === "match?" || operator === "any-match?";
        const matchAll = !operator.startsWith("any-");
        const captureName = steps[1].name;
        const regex = new RegExp(steps[2].value);
        textPredicates[index].push((captures) => {
            const nodes = [];
            for (const c of captures) {
                if (c.name === captureName)
                    nodes.push(c.node.text);
            }
            const test = /* @__PURE__ */ __name((text, positive) => {
                return positive ? regex.test(text) : !regex.test(text);
            }, "test");
            if (nodes.length === 0)
                return !isPositive;
            return matchAll ? nodes.every((text) => test(text, isPositive)) : nodes.some((text) => test(text, isPositive));
        });
    }
    __name(parseMatchPredicate, "parseMatchPredicate");
    function parseAnyOfPredicate(steps, index, operator, textPredicates) {
        if (steps.length < 2) {
            throw new Error(`Wrong number of arguments to \`#${operator}\` predicate. Expected at least 1. Got ${steps.length - 1}.`);
        }
        if (steps[1].type !== "capture") {
            throw new Error(`First argument of \`#${operator}\` predicate must be a capture. Got "${steps[1].value}".`);
        }
        const isPositive = operator === "any-of?";
        const captureName = steps[1].name;
        const stringSteps = steps.slice(2);
        if (!stringSteps.every(isStringStep)) {
            throw new Error(`Arguments to \`#${operator}\` predicate must be strings.".`);
        }
        const values = stringSteps.map((s) => s.value);
        textPredicates[index].push((captures) => {
            const nodes = [];
            for (const c of captures) {
                if (c.name === captureName)
                    nodes.push(c.node.text);
            }
            if (nodes.length === 0)
                return !isPositive;
            return nodes.every((text) => values.includes(text)) === isPositive;
        });
    }
    __name(parseAnyOfPredicate, "parseAnyOfPredicate");
    function parseIsPredicate(steps, index, operator, assertedProperties, refutedProperties) {
        if (steps.length < 2 || steps.length > 3) {
            throw new Error(`Wrong number of arguments to \`#${operator}\` predicate. Expected 1 or 2. Got ${steps.length - 1}.`);
        }
        if (!steps.every(isStringStep)) {
            throw new Error(`Arguments to \`#${operator}\` predicate must be strings.".`);
        }
        const properties = operator === "is?" ? assertedProperties : refutedProperties;
        if (!properties[index])
            properties[index] = {};
        properties[index][steps[1].value] = steps[2]?.value ?? null;
    }
    __name(parseIsPredicate, "parseIsPredicate");
    function parseSetDirective(steps, index, setProperties) {
        if (steps.length < 2 || steps.length > 3) {
            throw new Error(`Wrong number of arguments to \`#set!\` predicate. Expected 1 or 2. Got ${steps.length - 1}.`);
        }
        if (!steps.every(isStringStep)) {
            throw new Error(`Arguments to \`#set!\` predicate must be strings.".`);
        }
        if (!setProperties[index])
            setProperties[index] = {};
        setProperties[index][steps[1].value] = steps[2]?.value ?? null;
    }
    __name(parseSetDirective, "parseSetDirective");
    function parsePattern(index, stepType, stepValueId, captureNames, stringValues, steps, textPredicates, predicates, setProperties, assertedProperties, refutedProperties) {
        if (stepType === PREDICATE_STEP_TYPE_CAPTURE) {
            const name2 = captureNames[stepValueId];
            steps.push({ type: "capture", name: name2 });
        }
        else if (stepType === PREDICATE_STEP_TYPE_STRING) {
            steps.push({ type: "string", value: stringValues[stepValueId] });
        }
        else if (steps.length > 0) {
            if (steps[0].type !== "string") {
                throw new Error("Predicates must begin with a literal value");
            }
            const operator = steps[0].value;
            switch (operator) {
                case "any-not-eq?":
                case "not-eq?":
                case "any-eq?":
                case "eq?":
                    parseAnyPredicate(steps, index, operator, textPredicates);
                    break;
                case "any-not-match?":
                case "not-match?":
                case "any-match?":
                case "match?":
                    parseMatchPredicate(steps, index, operator, textPredicates);
                    break;
                case "not-any-of?":
                case "any-of?":
                    parseAnyOfPredicate(steps, index, operator, textPredicates);
                    break;
                case "is?":
                case "is-not?":
                    parseIsPredicate(steps, index, operator, assertedProperties, refutedProperties);
                    break;
                case "set!":
                    parseSetDirective(steps, index, setProperties);
                    break;
                default:
                    predicates[index].push({ operator, operands: steps.slice(1) });
            }
            steps.length = 0;
        }
    }
    __name(parsePattern, "parsePattern");
    var Query = class {
        static {
            __name(this, "Query");
        }
        /**
         * Create a new query from a string containing one or more S-expression
         * patterns.
         *
         * The query is associated with a particular language, and can only be run
         * on syntax nodes parsed with that language. References to Queries can be
         * shared between multiple threads.
         *
         * @link {@see https://tree-sitter.github.io/tree-sitter/using-parsers/queries}
         */
        constructor(language, source) {
            /** @internal */
            this[0] = 0;
            const sourceLength = C.lengthBytesUTF8(source);
            const sourceAddress = C._malloc(sourceLength + 1);
            C.stringToUTF8(source, sourceAddress, sourceLength + 1);
            const address = C._ts_query_new(language[0], sourceAddress, sourceLength, TRANSFER_BUFFER, TRANSFER_BUFFER + SIZE_OF_INT);
            if (!address) {
                const errorId = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
                const errorByte = C.getValue(TRANSFER_BUFFER, "i32");
                const errorIndex = C.UTF8ToString(sourceAddress, errorByte).length;
                const suffix = source.slice(errorIndex, errorIndex + 100).split("\n")[0];
                const word = suffix.match(QUERY_WORD_REGEX)?.[0] ?? "";
                C._free(sourceAddress);
                switch (errorId) {
                    case QueryErrorKind.Syntax:
                        throw new QueryError(QueryErrorKind.Syntax, { suffix: `${errorIndex}: '${suffix}'...` }, errorIndex, 0);
                    case QueryErrorKind.NodeName:
                        throw new QueryError(errorId, { word }, errorIndex, word.length);
                    case QueryErrorKind.FieldName:
                        throw new QueryError(errorId, { word }, errorIndex, word.length);
                    case QueryErrorKind.CaptureName:
                        throw new QueryError(errorId, { word }, errorIndex, word.length);
                    case QueryErrorKind.PatternStructure:
                        throw new QueryError(errorId, { suffix: `${errorIndex}: '${suffix}'...` }, errorIndex, 0);
                }
            }
            const stringCount = C._ts_query_string_count(address);
            const captureCount = C._ts_query_capture_count(address);
            const patternCount = C._ts_query_pattern_count(address);
            const captureNames = new Array(captureCount);
            const captureQuantifiers = new Array(patternCount);
            const stringValues = new Array(stringCount);
            for (let i2 = 0; i2 < captureCount; i2++) {
                const nameAddress = C._ts_query_capture_name_for_id(address, i2, TRANSFER_BUFFER);
                const nameLength = C.getValue(TRANSFER_BUFFER, "i32");
                captureNames[i2] = C.UTF8ToString(nameAddress, nameLength);
            }
            for (let i2 = 0; i2 < patternCount; i2++) {
                const captureQuantifiersArray = new Array(captureCount);
                for (let j = 0; j < captureCount; j++) {
                    const quantifier = C._ts_query_capture_quantifier_for_id(address, i2, j);
                    captureQuantifiersArray[j] = quantifier;
                }
                captureQuantifiers[i2] = captureQuantifiersArray;
            }
            for (let i2 = 0; i2 < stringCount; i2++) {
                const valueAddress = C._ts_query_string_value_for_id(address, i2, TRANSFER_BUFFER);
                const nameLength = C.getValue(TRANSFER_BUFFER, "i32");
                stringValues[i2] = C.UTF8ToString(valueAddress, nameLength);
            }
            const setProperties = new Array(patternCount);
            const assertedProperties = new Array(patternCount);
            const refutedProperties = new Array(patternCount);
            const predicates = new Array(patternCount);
            const textPredicates = new Array(patternCount);
            for (let i2 = 0; i2 < patternCount; i2++) {
                const predicatesAddress = C._ts_query_predicates_for_pattern(address, i2, TRANSFER_BUFFER);
                const stepCount = C.getValue(TRANSFER_BUFFER, "i32");
                predicates[i2] = [];
                textPredicates[i2] = [];
                const steps = new Array();
                let stepAddress = predicatesAddress;
                for (let j = 0; j < stepCount; j++) {
                    const stepType = C.getValue(stepAddress, "i32");
                    stepAddress += SIZE_OF_INT;
                    const stepValueId = C.getValue(stepAddress, "i32");
                    stepAddress += SIZE_OF_INT;
                    parsePattern(i2, stepType, stepValueId, captureNames, stringValues, steps, textPredicates, predicates, setProperties, assertedProperties, refutedProperties);
                }
                Object.freeze(textPredicates[i2]);
                Object.freeze(predicates[i2]);
                Object.freeze(setProperties[i2]);
                Object.freeze(assertedProperties[i2]);
                Object.freeze(refutedProperties[i2]);
            }
            C._free(sourceAddress);
            this[0] = address;
            this.captureNames = captureNames;
            this.captureQuantifiers = captureQuantifiers;
            this.textPredicates = textPredicates;
            this.predicates = predicates;
            this.setProperties = setProperties;
            this.assertedProperties = assertedProperties;
            this.refutedProperties = refutedProperties;
            this.exceededMatchLimit = false;
        }
        /** Delete the query, freeing its resources. */
        delete() {
            C._ts_query_delete(this[0]);
            this[0] = 0;
        }
        /**
         * Iterate over all of the matches in the order that they were found.
         *
         * Each match contains the index of the pattern that matched, and a list of
         * captures. Because multiple patterns can match the same set of nodes,
         * one match may contain captures that appear *before* some of the
         * captures from a previous match.
         *
         * @param {Node} node - The node to execute the query on.
         *
         * @param {QueryOptions} options - Options for query execution.
         */
        matches(node, options = {}) {
            const startPosition = options.startPosition ?? ZERO_POINT;
            const endPosition = options.endPosition ?? ZERO_POINT;
            const startIndex = options.startIndex ?? 0;
            const endIndex = options.endIndex ?? 0;
            const matchLimit = options.matchLimit ?? 4294967295;
            const maxStartDepth = options.maxStartDepth ?? 4294967295;
            const timeoutMicros = options.timeoutMicros ?? 0;
            const progressCallback = options.progressCallback;
            if (typeof matchLimit !== "number") {
                throw new Error("Arguments must be numbers");
            }
            this.matchLimit = matchLimit;
            if (endIndex !== 0 && startIndex > endIndex) {
                throw new Error("`startIndex` cannot be greater than `endIndex`");
            }
            if (endPosition !== ZERO_POINT && (startPosition.row > endPosition.row || startPosition.row === endPosition.row && startPosition.column > endPosition.column)) {
                throw new Error("`startPosition` cannot be greater than `endPosition`");
            }
            if (progressCallback) {
                C.currentQueryProgressCallback = progressCallback;
            }
            marshalNode(node);
            C._ts_query_matches_wasm(this[0], node.tree[0], startPosition.row, startPosition.column, endPosition.row, endPosition.column, startIndex, endIndex, matchLimit, maxStartDepth, timeoutMicros);
            const rawCount = C.getValue(TRANSFER_BUFFER, "i32");
            const startAddress = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const didExceedMatchLimit = C.getValue(TRANSFER_BUFFER + 2 * SIZE_OF_INT, "i32");
            const result = new Array(rawCount);
            this.exceededMatchLimit = Boolean(didExceedMatchLimit);
            let filteredCount = 0;
            let address = startAddress;
            for (let i2 = 0; i2 < rawCount; i2++) {
                const patternIndex = C.getValue(address, "i32");
                address += SIZE_OF_INT;
                const captureCount = C.getValue(address, "i32");
                address += SIZE_OF_INT;
                const captures = new Array(captureCount);
                address = unmarshalCaptures(this, node.tree, address, patternIndex, captures);
                if (this.textPredicates[patternIndex].every((p) => p(captures))) {
                    result[filteredCount] = { pattern: patternIndex, patternIndex, captures };
                    const setProperties = this.setProperties[patternIndex];
                    result[filteredCount].setProperties = setProperties;
                    const assertedProperties = this.assertedProperties[patternIndex];
                    result[filteredCount].assertedProperties = assertedProperties;
                    const refutedProperties = this.refutedProperties[patternIndex];
                    result[filteredCount].refutedProperties = refutedProperties;
                    filteredCount++;
                }
            }
            result.length = filteredCount;
            C._free(startAddress);
            C.currentQueryProgressCallback = null;
            return result;
        }
        /**
         * Iterate over all of the individual captures in the order that they
         * appear.
         *
         * This is useful if you don't care about which pattern matched, and just
         * want a single, ordered sequence of captures.
         *
         * @param {Node} node - The node to execute the query on.
         *
         * @param {QueryOptions} options - Options for query execution.
         */
        captures(node, options = {}) {
            const startPosition = options.startPosition ?? ZERO_POINT;
            const endPosition = options.endPosition ?? ZERO_POINT;
            const startIndex = options.startIndex ?? 0;
            const endIndex = options.endIndex ?? 0;
            const matchLimit = options.matchLimit ?? 4294967295;
            const maxStartDepth = options.maxStartDepth ?? 4294967295;
            const timeoutMicros = options.timeoutMicros ?? 0;
            const progressCallback = options.progressCallback;
            if (typeof matchLimit !== "number") {
                throw new Error("Arguments must be numbers");
            }
            this.matchLimit = matchLimit;
            if (endIndex !== 0 && startIndex > endIndex) {
                throw new Error("`startIndex` cannot be greater than `endIndex`");
            }
            if (endPosition !== ZERO_POINT && (startPosition.row > endPosition.row || startPosition.row === endPosition.row && startPosition.column > endPosition.column)) {
                throw new Error("`startPosition` cannot be greater than `endPosition`");
            }
            if (progressCallback) {
                C.currentQueryProgressCallback = progressCallback;
            }
            marshalNode(node);
            C._ts_query_captures_wasm(this[0], node.tree[0], startPosition.row, startPosition.column, endPosition.row, endPosition.column, startIndex, endIndex, matchLimit, maxStartDepth, timeoutMicros);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const startAddress = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const didExceedMatchLimit = C.getValue(TRANSFER_BUFFER + 2 * SIZE_OF_INT, "i32");
            const result = new Array();
            this.exceededMatchLimit = Boolean(didExceedMatchLimit);
            const captures = new Array();
            let address = startAddress;
            for (let i2 = 0; i2 < count; i2++) {
                const patternIndex = C.getValue(address, "i32");
                address += SIZE_OF_INT;
                const captureCount = C.getValue(address, "i32");
                address += SIZE_OF_INT;
                const captureIndex = C.getValue(address, "i32");
                address += SIZE_OF_INT;
                captures.length = captureCount;
                address = unmarshalCaptures(this, node.tree, address, patternIndex, captures);
                if (this.textPredicates[patternIndex].every((p) => p(captures))) {
                    const capture = captures[captureIndex];
                    const setProperties = this.setProperties[patternIndex];
                    capture.setProperties = setProperties;
                    const assertedProperties = this.assertedProperties[patternIndex];
                    capture.assertedProperties = assertedProperties;
                    const refutedProperties = this.refutedProperties[patternIndex];
                    capture.refutedProperties = refutedProperties;
                    result.push(capture);
                }
            }
            C._free(startAddress);
            C.currentQueryProgressCallback = null;
            return result;
        }
        /** Get the predicates for a given pattern. */
        predicatesForPattern(patternIndex) {
            return this.predicates[patternIndex];
        }
        /**
         * Disable a certain capture within a query.
         *
         * This prevents the capture from being returned in matches, and also
         * avoids any resource usage associated with recording the capture.
         */
        disableCapture(captureName) {
            const captureNameLength = C.lengthBytesUTF8(captureName);
            const captureNameAddress = C._malloc(captureNameLength + 1);
            C.stringToUTF8(captureName, captureNameAddress, captureNameLength + 1);
            C._ts_query_disable_capture(this[0], captureNameAddress, captureNameLength);
            C._free(captureNameAddress);
        }
        /**
         * Disable a certain pattern within a query.
         *
         * This prevents the pattern from matching, and also avoids any resource
         * usage associated with the pattern. This throws an error if the pattern
         * index is out of bounds.
         */
        disablePattern(patternIndex) {
            if (patternIndex >= this.predicates.length) {
                throw new Error(`Pattern index is ${patternIndex} but the pattern count is ${this.predicates.length}`);
            }
            C._ts_query_disable_pattern(this[0], patternIndex);
        }
        /**
         * Check if, on its last execution, this cursor exceeded its maximum number
         * of in-progress matches.
         */
        didExceedMatchLimit() {
            return this.exceededMatchLimit;
        }
        /** Get the byte offset where the given pattern starts in the query's source. */
        startIndexForPattern(patternIndex) {
            if (patternIndex >= this.predicates.length) {
                throw new Error(`Pattern index is ${patternIndex} but the pattern count is ${this.predicates.length}`);
            }
            return C._ts_query_start_byte_for_pattern(this[0], patternIndex);
        }
        /** Get the byte offset where the given pattern ends in the query's source. */
        endIndexForPattern(patternIndex) {
            if (patternIndex >= this.predicates.length) {
                throw new Error(`Pattern index is ${patternIndex} but the pattern count is ${this.predicates.length}`);
            }
            return C._ts_query_end_byte_for_pattern(this[0], patternIndex);
        }
        /** Get the number of patterns in the query. */
        patternCount() {
            return C._ts_query_pattern_count(this[0]);
        }
        /** Get the index for a given capture name. */
        captureIndexForName(captureName) {
            return this.captureNames.indexOf(captureName);
        }
        /** Check if a given pattern within a query has a single root node. */
        isPatternRooted(patternIndex) {
            return C._ts_query_is_pattern_rooted(this[0], patternIndex) === 1;
        }
        /** Check if a given pattern within a query has a single root node. */
        isPatternNonLocal(patternIndex) {
            return C._ts_query_is_pattern_non_local(this[0], patternIndex) === 1;
        }
        /**
         * Check if a given step in a query is 'definite'.
         *
         * A query step is 'definite' if its parent pattern will be guaranteed to
         * match successfully once it reaches the step.
         */
        isPatternGuaranteedAtStep(byteIndex) {
            return C._ts_query_is_pattern_guaranteed_at_step(this[0], byteIndex) === 1;
        }
    };
    // src/language.ts
    var LANGUAGE_FUNCTION_REGEX = /^tree_sitter_\w+$/;
    var Language = class _Language {
        static {
            __name(this, "Language");
        }
        /** @internal */
        constructor(internal, address) {
            /** @internal */
            this[0] = 0;
            assertInternal(internal);
            this[0] = address;
            this.types = new Array(C._ts_language_symbol_count(this[0]));
            for (let i2 = 0, n = this.types.length; i2 < n; i2++) {
                if (C._ts_language_symbol_type(this[0], i2) < 2) {
                    this.types[i2] = C.UTF8ToString(C._ts_language_symbol_name(this[0], i2));
                }
            }
            this.fields = new Array(C._ts_language_field_count(this[0]) + 1);
            for (let i2 = 0, n = this.fields.length; i2 < n; i2++) {
                const fieldName = C._ts_language_field_name_for_id(this[0], i2);
                if (fieldName !== 0) {
                    this.fields[i2] = C.UTF8ToString(fieldName);
                }
                else {
                    this.fields[i2] = null;
                }
            }
        }
        /**
         * Gets the name of the language.
         */
        get name() {
            const ptr = C._ts_language_name(this[0]);
            if (ptr === 0)
                return null;
            return C.UTF8ToString(ptr);
        }
        /**
         * @deprecated since version 0.25.0, use {@link Language#abiVersion} instead
         * Gets the version of the language.
         */
        get version() {
            return C._ts_language_version(this[0]);
        }
        /**
         * Gets the ABI version of the language.
         */
        get abiVersion() {
            return C._ts_language_abi_version(this[0]);
        }
        /**
        * Get the metadata for this language. This information is generated by the
        * CLI, and relies on the language author providing the correct metadata in
        * the language's `tree-sitter.json` file.
        */
        get metadata() {
            C._ts_language_metadata(this[0]);
            const length = C.getValue(TRANSFER_BUFFER, "i32");
            const address = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            if (length === 0)
                return null;
            return unmarshalLanguageMetadata(address);
        }
        /**
         * Gets the number of fields in the language.
         */
        get fieldCount() {
            return this.fields.length - 1;
        }
        /**
         * Gets the number of states in the language.
         */
        get stateCount() {
            return C._ts_language_state_count(this[0]);
        }
        /**
         * Get the field id for a field name.
         */
        fieldIdForName(fieldName) {
            const result = this.fields.indexOf(fieldName);
            return result !== -1 ? result : null;
        }
        /**
         * Get the field name for a field id.
         */
        fieldNameForId(fieldId) {
            return this.fields[fieldId] ?? null;
        }
        /**
         * Get the node type id for a node type name.
         */
        idForNodeType(type, named) {
            const typeLength = C.lengthBytesUTF8(type);
            const typeAddress = C._malloc(typeLength + 1);
            C.stringToUTF8(type, typeAddress, typeLength + 1);
            const result = C._ts_language_symbol_for_name(this[0], typeAddress, typeLength, named ? 1 : 0);
            C._free(typeAddress);
            return result || null;
        }
        /**
         * Gets the number of node types in the language.
         */
        get nodeTypeCount() {
            return C._ts_language_symbol_count(this[0]);
        }
        /**
         * Get the node type name for a node type id.
         */
        nodeTypeForId(typeId) {
            const name2 = C._ts_language_symbol_name(this[0], typeId);
            return name2 ? C.UTF8ToString(name2) : null;
        }
        /**
         * Check if a node type is named.
         *
         * @see {@link https://tree-sitter.github.io/tree-sitter/using-parsers/2-basic-parsing.html#named-vs-anonymous-nodes}
         */
        nodeTypeIsNamed(typeId) {
            return C._ts_language_type_is_named_wasm(this[0], typeId) ? true : false;
        }
        /**
         * Check if a node type is visible.
         */
        nodeTypeIsVisible(typeId) {
            return C._ts_language_type_is_visible_wasm(this[0], typeId) ? true : false;
        }
        /**
         * Get the supertypes ids of this language.
         *
         * @see {@link https://tree-sitter.github.io/tree-sitter/using-parsers/6-static-node-types.html?highlight=supertype#supertype-nodes}
         */
        get supertypes() {
            C._ts_language_supertypes_wasm(this[0]);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(count);
            if (count > 0) {
                let address = buffer;
                for (let i2 = 0; i2 < count; i2++) {
                    result[i2] = C.getValue(address, "i16");
                    address += SIZE_OF_SHORT;
                }
            }
            return result;
        }
        /**
         * Get the subtype ids for a given supertype node id.
         */
        subtypes(supertype) {
            C._ts_language_subtypes_wasm(this[0], supertype);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(count);
            if (count > 0) {
                let address = buffer;
                for (let i2 = 0; i2 < count; i2++) {
                    result[i2] = C.getValue(address, "i16");
                    address += SIZE_OF_SHORT;
                }
            }
            return result;
        }
        /**
         * Get the next state id for a given state id and node type id.
         */
        nextState(stateId, typeId) {
            return C._ts_language_next_state(this[0], stateId, typeId);
        }
        /**
         * Create a new lookahead iterator for this language and parse state.
         *
         * This returns `null` if state is invalid for this language.
         *
         * Iterating {@link LookaheadIterator} will yield valid symbols in the given
         * parse state. Newly created lookahead iterators will return the `ERROR`
         * symbol from {@link LookaheadIterator#currentType}.
         *
         * Lookahead iterators can be useful for generating suggestions and improving
         * syntax error diagnostics. To get symbols valid in an `ERROR` node, use the
         * lookahead iterator on its first leaf node state. For `MISSING` nodes, a
         * lookahead iterator created on the previous non-extra leaf node may be
         * appropriate.
         */
        lookaheadIterator(stateId) {
            const address = C._ts_lookahead_iterator_new(this[0], stateId);
            if (address)
                return new LookaheadIterator(INTERNAL, address, this);
            return null;
        }
        /**
         * @deprecated since version 0.25.0, call `new` on a {@link Query} instead
         *
         * Create a new query from a string containing one or more S-expression
         * patterns.
         *
         * The query is associated with a particular language, and can only be run
         * on syntax nodes parsed with that language. References to Queries can be
         * shared between multiple threads.
         *
         * @link {@see https://tree-sitter.github.io/tree-sitter/using-parsers/queries}
         */
        query(source) {
            console.warn("Language.query is deprecated. Use new Query(language, source) instead.");
            return new Query(this, source);
        }
        /**
         * Load a language from a WebAssembly module.
         * The module can be provided as a path to a file or as a buffer.
         */
        static async load(input) {
            let bytes;
            if (input instanceof Uint8Array) {
                bytes = Promise.resolve(input);
            }
            else {
                if (globalThis.process?.versions.node) {
                    const fs2 = __require("fs/promises");
                    bytes = fs2.readFile(input);
                }
                else {
                    bytes = fetch(input).then((response) => response.arrayBuffer().then((buffer) => {
                        if (response.ok) {
                            return new Uint8Array(buffer);
                        }
                        else {
                            const body2 = new TextDecoder("utf-8").decode(buffer);
                            throw new Error(`Language.load failed with status ${response.status}.

${body2}`);
                        }
                    }));
                }
            }
            const mod = await C.loadWebAssemblyModule(await bytes, { loadAsync: true });
            const symbolNames = Object.keys(mod);
            const functionName = symbolNames.find((key) => LANGUAGE_FUNCTION_REGEX.test(key) && !key.includes("external_scanner_"));
            if (!functionName) {
                console.log(`Couldn't find language function in WASM file. Symbols:
${JSON.stringify(symbolNames, null, 2)}`);
                throw new Error("Language.load failed: no language function found in WASM file");
            }
            const languageAddress = mod[functionName]();
            return new _Language(INTERNAL, languageAddress);
        }
    };
    // lib/tree-sitter.mjs
    var Module2 = (() => {
        var _scriptName = getCurrentScriptUrl();
        return async function (moduleArg = {}) {
            var moduleRtn;
            var Module = moduleArg;
            var readyPromiseResolve, readyPromiseReject;
            var readyPromise = new Promise((resolve, reject) => {
                readyPromiseResolve = resolve;
                readyPromiseReject = reject;
            });
            var ENVIRONMENT_IS_WEB = typeof window == "object";
            var ENVIRONMENT_IS_WORKER = typeof importScripts == "function";
            var ENVIRONMENT_IS_NODE = typeof process == "object" && typeof process.versions == "object" && typeof process.versions.node == "string";
            var ENVIRONMENT_IS_SHELL = !ENVIRONMENT_IS_WEB && !ENVIRONMENT_IS_NODE && !ENVIRONMENT_IS_WORKER;
            if (ENVIRONMENT_IS_NODE) {
                const { createRequire } = await import("module");
                var require = createRequire(getCurrentScriptUrl());
            }
            Module.currentQueryProgressCallback = null;
            Module.currentProgressCallback = null;
            Module.currentLogCallback = null;
            Module.currentParseCallback = null;
            var moduleOverrides = Object.assign({}, Module);
            var arguments_ = [];
            var thisProgram = "./this.program";
            var quit_ = /* @__PURE__ */ __name((status, toThrow) => {
                throw toThrow;
            }, "quit_");
            var scriptDirectory = "";
            function locateFile(path) {
                if (Module["locateFile"]) {
                    return Module["locateFile"](path, scriptDirectory);
                }
                return scriptDirectory + path;
            }
            __name(locateFile, "locateFile");
            var readAsync, readBinary;
            if (ENVIRONMENT_IS_NODE) {
                var fs = require("fs");
                var nodePath = require("path");
                scriptDirectory = require("url").fileURLToPath(new URL("./", getCurrentScriptUrl()));
                readBinary = /* @__PURE__ */ __name((filename) => {
                    filename = isFileURI(filename) ? new URL(filename) : nodePath.normalize(filename);
                    var ret = fs.readFileSync(filename);
                    return ret;
                }, "readBinary");
                readAsync = /* @__PURE__ */ __name((filename, binary2 = true) => {
                    filename = isFileURI(filename) ? new URL(filename) : nodePath.normalize(filename);
                    return new Promise((resolve, reject) => {
                        fs.readFile(filename, binary2 ? void 0 : "utf8", (err2, data) => {
                            if (err2)
                                reject(err2);
                            else
                                resolve(binary2 ? data.buffer : data);
                        });
                    });
                }, "readAsync");
                if (!Module["thisProgram"] && process.argv.length > 1) {
                    thisProgram = process.argv[1].replace(/\\/g, "/");
                }
                arguments_ = process.argv.slice(2);
                quit_ = /* @__PURE__ */ __name((status, toThrow) => {
                    process.exitCode = status;
                    throw toThrow;
                }, "quit_");
            }
            else if (ENVIRONMENT_IS_WEB || ENVIRONMENT_IS_WORKER) {
                if (ENVIRONMENT_IS_WORKER) {
                    scriptDirectory = self.location.href;
                }
                else if (typeof document != "undefined" && document.currentScript) {
                    scriptDirectory = document.currentScript.src;
                }
                if (_scriptName) {
                    scriptDirectory = _scriptName;
                }
                if (scriptDirectory.startsWith("blob:")) {
                    scriptDirectory = "";
                }
                else {
                    scriptDirectory = scriptDirectory.substr(0, scriptDirectory.replace(/[?#].*/, "").lastIndexOf("/") + 1);
                }
                {
                    if (ENVIRONMENT_IS_WORKER) {
                        readBinary = /* @__PURE__ */ __name((url) => {
                            var xhr = new XMLHttpRequest();
                            xhr.open("GET", url, false);
                            xhr.responseType = "arraybuffer";
                            xhr.send(null);
                            return new Uint8Array(
                            /** @type{!ArrayBuffer} */
                            xhr.response);
                        }, "readBinary");
                    }
                    readAsync = /* @__PURE__ */ __name((url) => {
                        if (isFileURI(url)) {
                            return new Promise((reject, resolve) => {
                                var xhr = new XMLHttpRequest();
                                xhr.open("GET", url, true);
                                xhr.responseType = "arraybuffer";
                                xhr.onload = () => {
                                    if (xhr.status == 200 || xhr.status == 0 && xhr.response) {
                                        resolve(xhr.response);
                                    }
                                    reject(xhr.status);
                                };
                                xhr.onerror = reject;
                                xhr.send(null);
                            });
                        }
                        return fetch(url, {
                            credentials: "same-origin"
                        }).then((response) => {
                            if (response.ok) {
                                return response.arrayBuffer();
                            }
                            return Promise.reject(new Error(response.status + " : " + response.url));
                        });
                    }, "readAsync");
                }
            }
            else {
            }
            var out = Module["print"] || console.log.bind(console);
            var err = Module["printErr"] || console.error.bind(console);
            Object.assign(Module, moduleOverrides);
            moduleOverrides = null;
            if (Module["arguments"])
                arguments_ = Module["arguments"];
            if (Module["thisProgram"])
                thisProgram = Module["thisProgram"];
            if (Module["quit"])
                quit_ = Module["quit"];
            var dynamicLibraries = Module["dynamicLibraries"] || [];
            var wasmBinary;
            if (Module["wasmBinary"])
                wasmBinary = Module["wasmBinary"];
            var wasmMemory;
            var ABORT = false;
            var EXITSTATUS;
            function assert(condition, text) {
                if (!condition) {
                    abort(text);
                }
            }
            __name(assert, "assert");
            var HEAP, HEAP8, HEAPU8, HEAP16, HEAPU16, HEAP32, HEAPU32, HEAPF32, HEAPF64;
            var HEAP_DATA_VIEW;
            function updateMemoryViews() {
                var b = wasmMemory.buffer;
                Module["HEAP_DATA_VIEW"] = HEAP_DATA_VIEW = new DataView(b);
                Module["HEAP8"] = HEAP8 = new Int8Array(b);
                Module["HEAP16"] = HEAP16 = new Int16Array(b);
                Module["HEAPU8"] = HEAPU8 = new Uint8Array(b);
                Module["HEAPU16"] = HEAPU16 = new Uint16Array(b);
                Module["HEAP32"] = HEAP32 = new Int32Array(b);
                Module["HEAPU32"] = HEAPU32 = new Uint32Array(b);
                Module["HEAPF32"] = HEAPF32 = new Float32Array(b);
                Module["HEAPF64"] = HEAPF64 = new Float64Array(b);
            }
            __name(updateMemoryViews, "updateMemoryViews");
            if (Module["wasmMemory"]) {
                wasmMemory = Module["wasmMemory"];
            }
            else {
                var INITIAL_MEMORY = Module["INITIAL_MEMORY"] || 33554432;
                wasmMemory = new WebAssembly.Memory({
                    "initial": INITIAL_MEMORY / 65536,
                    // In theory we should not need to emit the maximum if we want "unlimited"
                    // or 4GB of memory, but VMs error on that atm, see
                    // https://github.com/emscripten-core/emscripten/issues/14130
                    // And in the pthreads case we definitely need to emit a maximum. So
                    // always emit one.
                    "maximum": 2147483648 / 65536
                });
            }
            updateMemoryViews();
            var __ATPRERUN__ = [];
            var __ATINIT__ = [];
            var __ATMAIN__ = [];
            var __ATEXIT__ = [];
            var __ATPOSTRUN__ = [];
            var __RELOC_FUNCS__ = [];
            var runtimeInitialized = false;
            function preRun() {
                if (Module["preRun"]) {
                    if (typeof Module["preRun"] == "function")
                        Module["preRun"] = [Module["preRun"]];
                    while (Module["preRun"].length) {
                        addOnPreRun(Module["preRun"].shift());
                    }
                }
                callRuntimeCallbacks(__ATPRERUN__);
            }
            __name(preRun, "preRun");
            function initRuntime() {
                runtimeInitialized = true;
                callRuntimeCallbacks(__RELOC_FUNCS__);
                callRuntimeCallbacks(__ATINIT__);
            }
            __name(initRuntime, "initRuntime");
            function preMain() {
                callRuntimeCallbacks(__ATMAIN__);
            }
            __name(preMain, "preMain");
            function postRun() {
                if (Module["postRun"]) {
                    if (typeof Module["postRun"] == "function")
                        Module["postRun"] = [Module["postRun"]];
                    while (Module["postRun"].length) {
                        addOnPostRun(Module["postRun"].shift());
                    }
                }
                callRuntimeCallbacks(__ATPOSTRUN__);
            }
            __name(postRun, "postRun");
            function addOnPreRun(cb) {
                __ATPRERUN__.unshift(cb);
            }
            __name(addOnPreRun, "addOnPreRun");
            function addOnInit(cb) {
                __ATINIT__.unshift(cb);
            }
            __name(addOnInit, "addOnInit");
            function addOnPreMain(cb) {
                __ATMAIN__.unshift(cb);
            }
            __name(addOnPreMain, "addOnPreMain");
            function addOnExit(cb) {
            }
            __name(addOnExit, "addOnExit");
            function addOnPostRun(cb) {
                __ATPOSTRUN__.unshift(cb);
            }
            __name(addOnPostRun, "addOnPostRun");
            var runDependencies = 0;
            var runDependencyWatcher = null;
            var dependenciesFulfilled = null;
            function getUniqueRunDependency(id) {
                return id;
            }
            __name(getUniqueRunDependency, "getUniqueRunDependency");
            function addRunDependency(id) {
                runDependencies++;
                Module["monitorRunDependencies"]?.(runDependencies);
            }
            __name(addRunDependency, "addRunDependency");
            function removeRunDependency(id) {
                runDependencies--;
                Module["monitorRunDependencies"]?.(runDependencies);
                if (runDependencies == 0) {
                    if (runDependencyWatcher !== null) {
                        clearInterval(runDependencyWatcher);
                        runDependencyWatcher = null;
                    }
                    if (dependenciesFulfilled) {
                        var callback = dependenciesFulfilled;
                        dependenciesFulfilled = null;
                        callback();
                    }
                }
            }
            __name(removeRunDependency, "removeRunDependency");
            function abort(what) {
                Module["onAbort"]?.(what);
                what = "Aborted(" + what + ")";
                err(what);
                ABORT = true;
                EXITSTATUS = 1;
                what += ". Build with -sASSERTIONS for more info.";
                var e = new WebAssembly.RuntimeError(what);
                readyPromiseReject(e);
                throw e;
            }
            __name(abort, "abort");
            var dataURIPrefix = "data:application/octet-stream;base64,";
            var isDataURI = /* @__PURE__ */ __name((filename) => filename.startsWith(dataURIPrefix), "isDataURI");
            var isFileURI = /* @__PURE__ */ __name((filename) => filename.startsWith("file://"), "isFileURI");
            function findWasmBinary() {
                if (Module["locateFile"]) {
                    var f = "tree-sitter.wasm";
                    if (!isDataURI(f)) {
                        return locateFile(f);
                    }
                    return f;
                }
                return new URL("tree-sitter.wasm", getCurrentScriptUrl()).href;
            }
            __name(findWasmBinary, "findWasmBinary");
            var wasmBinaryFile;
            function getBinarySync(file) {
                if (file == wasmBinaryFile && wasmBinary) {
                    return new Uint8Array(wasmBinary);
                }
                if (readBinary) {
                    return readBinary(file);
                }
                throw "both async and sync fetching of the wasm failed";
            }
            __name(getBinarySync, "getBinarySync");
            function getBinaryPromise(binaryFile) {
                if (!wasmBinary) {
                    return readAsync(binaryFile).then((response) => new Uint8Array(
                    /** @type{!ArrayBuffer} */
                    response), 
                    // Fall back to getBinarySync if readAsync fails
                    () => getBinarySync(binaryFile));
                }
                return Promise.resolve().then(() => getBinarySync(binaryFile));
            }
            __name(getBinaryPromise, "getBinaryPromise");
            function instantiateArrayBuffer(binaryFile, imports, receiver) {
                return getBinaryPromise(binaryFile).then((binary2) => WebAssembly.instantiate(binary2, imports)).then(receiver, (reason) => {
                    err(`failed to asynchronously prepare wasm: ${reason}`);
                    abort(reason);
                });
            }
            __name(instantiateArrayBuffer, "instantiateArrayBuffer");
            function instantiateAsync(binary2, binaryFile, imports, callback) {
                if (!binary2 && typeof WebAssembly.instantiateStreaming == "function" && !isDataURI(binaryFile) && // Don't use streaming for file:// delivered objects in a webview, fetch them synchronously.
                    !isFileURI(binaryFile) && // Avoid instantiateStreaming() on Node.js environment for now, as while
                    // Node.js v18.1.0 implements it, it does not have a full fetch()
                    // implementation yet.
                    // Reference:
                    //   https://github.com/emscripten-core/emscripten/pull/16917
                    !ENVIRONMENT_IS_NODE && typeof fetch == "function") {
                    return fetch(binaryFile, {
                        credentials: "same-origin"
                    }).then((response) => {
                        var result = WebAssembly.instantiateStreaming(response, imports);
                        return result.then(callback, function (reason) {
                            err(`wasm streaming compile failed: ${reason}`);
                            err("falling back to ArrayBuffer instantiation");
                            return instantiateArrayBuffer(binaryFile, imports, callback);
                        });
                    });
                }
                return instantiateArrayBuffer(binaryFile, imports, callback);
            }
            __name(instantiateAsync, "instantiateAsync");
            function getWasmImports() {
                return {
                    "env": wasmImports,
                    "wasi_snapshot_preview1": wasmImports,
                    "GOT.mem": new Proxy(wasmImports, GOTHandler),
                    "GOT.func": new Proxy(wasmImports, GOTHandler)
                };
            }
            __name(getWasmImports, "getWasmImports");
            function createWasm() {
                var info2 = getWasmImports();
                function receiveInstance(instance2, module2) {
                    wasmExports = instance2.exports;
                    wasmExports = relocateExports(wasmExports, 1024);
                    var metadata2 = getDylinkMetadata(module2);
                    if (metadata2.neededDynlibs) {
                        dynamicLibraries = metadata2.neededDynlibs.concat(dynamicLibraries);
                    }
                    mergeLibSymbols(wasmExports, "main");
                    LDSO.init();
                    loadDylibs();
                    addOnInit(wasmExports["__wasm_call_ctors"]);
                    __RELOC_FUNCS__.push(wasmExports["__wasm_apply_data_relocs"]);
                    removeRunDependency("wasm-instantiate");
                    return wasmExports;
                }
                __name(receiveInstance, "receiveInstance");
                addRunDependency("wasm-instantiate");
                function receiveInstantiationResult(result) {
                    receiveInstance(result["instance"], result["module"]);
                }
                __name(receiveInstantiationResult, "receiveInstantiationResult");
                if (Module["instantiateWasm"]) {
                    try {
                        return Module["instantiateWasm"](info2, receiveInstance);
                    }
                    catch (e) {
                        err(`Module.instantiateWasm callback failed with error: ${e}`);
                        readyPromiseReject(e);
                    }
                }
                if (!wasmBinaryFile)
                    wasmBinaryFile = findWasmBinary();
                instantiateAsync(wasmBinary, wasmBinaryFile, info2, receiveInstantiationResult).catch(readyPromiseReject);
                return {};
            }
            __name(createWasm, "createWasm");
            var tempDouble;
            var tempI64;
            var ASM_CONSTS = {};
            function ExitStatus(status) {
                this.name = "ExitStatus";
                this.message = `Program terminated with exit(${status})`;
                this.status = status;
            }
            __name(ExitStatus, "ExitStatus");
            var GOT = {};
            var currentModuleWeakSymbols = /* @__PURE__ */ new Set([]);
            var GOTHandler = {
                get(obj, symName) {
                    var rtn = GOT[symName];
                    if (!rtn) {
                        rtn = GOT[symName] = new WebAssembly.Global({
                            "value": "i32",
                            "mutable": true
                        });
                    }
                    if (!currentModuleWeakSymbols.has(symName)) {
                        rtn.required = true;
                    }
                    return rtn;
                }
            };
            var LE_HEAP_LOAD_F32 = /* @__PURE__ */ __name((byteOffset) => HEAP_DATA_VIEW.getFloat32(byteOffset, true), "LE_HEAP_LOAD_F32");
            var LE_HEAP_LOAD_F64 = /* @__PURE__ */ __name((byteOffset) => HEAP_DATA_VIEW.getFloat64(byteOffset, true), "LE_HEAP_LOAD_F64");
            var LE_HEAP_LOAD_I16 = /* @__PURE__ */ __name((byteOffset) => HEAP_DATA_VIEW.getInt16(byteOffset, true), "LE_HEAP_LOAD_I16");
            var LE_HEAP_LOAD_I32 = /* @__PURE__ */ __name((byteOffset) => HEAP_DATA_VIEW.getInt32(byteOffset, true), "LE_HEAP_LOAD_I32");
            var LE_HEAP_LOAD_U16 = /* @__PURE__ */ __name((byteOffset) => HEAP_DATA_VIEW.getUint16(byteOffset, true), "LE_HEAP_LOAD_U16");
            var LE_HEAP_LOAD_U32 = /* @__PURE__ */ __name((byteOffset) => HEAP_DATA_VIEW.getUint32(byteOffset, true), "LE_HEAP_LOAD_U32");
            var LE_HEAP_STORE_F32 = /* @__PURE__ */ __name((byteOffset, value) => HEAP_DATA_VIEW.setFloat32(byteOffset, value, true), "LE_HEAP_STORE_F32");
            var LE_HEAP_STORE_F64 = /* @__PURE__ */ __name((byteOffset, value) => HEAP_DATA_VIEW.setFloat64(byteOffset, value, true), "LE_HEAP_STORE_F64");
            var LE_HEAP_STORE_I16 = /* @__PURE__ */ __name((byteOffset, value) => HEAP_DATA_VIEW.setInt16(byteOffset, value, true), "LE_HEAP_STORE_I16");
            var LE_HEAP_STORE_I32 = /* @__PURE__ */ __name((byteOffset, value) => HEAP_DATA_VIEW.setInt32(byteOffset, value, true), "LE_HEAP_STORE_I32");
            var LE_HEAP_STORE_U16 = /* @__PURE__ */ __name((byteOffset, value) => HEAP_DATA_VIEW.setUint16(byteOffset, value, true), "LE_HEAP_STORE_U16");
            var LE_HEAP_STORE_U32 = /* @__PURE__ */ __name((byteOffset, value) => HEAP_DATA_VIEW.setUint32(byteOffset, value, true), "LE_HEAP_STORE_U32");
            var callRuntimeCallbacks = /* @__PURE__ */ __name((callbacks) => {
                while (callbacks.length > 0) {
                    callbacks.shift()(Module);
                }
            }, "callRuntimeCallbacks");
            var UTF8Decoder = typeof TextDecoder != "undefined" ? new TextDecoder() : void 0;
            var UTF8ArrayToString = /* @__PURE__ */ __name((heapOrArray, idx, maxBytesToRead) => {
                var endIdx = idx + maxBytesToRead;
                var endPtr = idx;
                while (heapOrArray[endPtr] && !(endPtr >= endIdx))
                    ++endPtr;
                if (endPtr - idx > 16 && heapOrArray.buffer && UTF8Decoder) {
                    return UTF8Decoder.decode(heapOrArray.subarray(idx, endPtr));
                }
                var str = "";
                while (idx < endPtr) {
                    var u0 = heapOrArray[idx++];
                    if (!(u0 & 128)) {
                        str += String.fromCharCode(u0);
                        continue;
                    }
                    var u1 = heapOrArray[idx++] & 63;
                    if ((u0 & 224) == 192) {
                        str += String.fromCharCode((u0 & 31) << 6 | u1);
                        continue;
                    }
                    var u2 = heapOrArray[idx++] & 63;
                    if ((u0 & 240) == 224) {
                        u0 = (u0 & 15) << 12 | u1 << 6 | u2;
                    }
                    else {
                        u0 = (u0 & 7) << 18 | u1 << 12 | u2 << 6 | heapOrArray[idx++] & 63;
                    }
                    if (u0 < 65536) {
                        str += String.fromCharCode(u0);
                    }
                    else {
                        var ch = u0 - 65536;
                        str += String.fromCharCode(55296 | ch >> 10, 56320 | ch & 1023);
                    }
                }
                return str;
            }, "UTF8ArrayToString");
            var getDylinkMetadata = /* @__PURE__ */ __name((binary2) => {
                var offset = 0;
                var end = 0;
                function getU8() {
                    return binary2[offset++];
                }
                __name(getU8, "getU8");
                function getLEB() {
                    var ret = 0;
                    var mul = 1;
                    while (1) {
                        var byte = binary2[offset++];
                        ret += (byte & 127) * mul;
                        mul *= 128;
                        if (!(byte & 128))
                            break;
                    }
                    return ret;
                }
                __name(getLEB, "getLEB");
                function getString() {
                    var len = getLEB();
                    offset += len;
                    return UTF8ArrayToString(binary2, offset - len, len);
                }
                __name(getString, "getString");
                function failIf(condition, message) {
                    if (condition)
                        throw new Error(message);
                }
                __name(failIf, "failIf");
                var name2 = "dylink.0";
                if (binary2 instanceof WebAssembly.Module) {
                    var dylinkSection = WebAssembly.Module.customSections(binary2, name2);
                    if (dylinkSection.length === 0) {
                        name2 = "dylink";
                        dylinkSection = WebAssembly.Module.customSections(binary2, name2);
                    }
                    failIf(dylinkSection.length === 0, "need dylink section");
                    binary2 = new Uint8Array(dylinkSection[0]);
                    end = binary2.length;
                }
                else {
                    var int32View = new Uint32Array(new Uint8Array(binary2.subarray(0, 24)).buffer);
                    var magicNumberFound = int32View[0] == 1836278016 || int32View[0] == 6386541;
                    failIf(!magicNumberFound, "need to see wasm magic number");
                    failIf(binary2[8] !== 0, "need the dylink section to be first");
                    offset = 9;
                    var section_size = getLEB();
                    end = offset + section_size;
                    name2 = getString();
                }
                var customSection = {
                    neededDynlibs: [],
                    tlsExports: /* @__PURE__ */ new Set(),
                    weakImports: /* @__PURE__ */ new Set()
                };
                if (name2 == "dylink") {
                    customSection.memorySize = getLEB();
                    customSection.memoryAlign = getLEB();
                    customSection.tableSize = getLEB();
                    customSection.tableAlign = getLEB();
                    var neededDynlibsCount = getLEB();
                    for (var i2 = 0; i2 < neededDynlibsCount; ++i2) {
                        var libname = getString();
                        customSection.neededDynlibs.push(libname);
                    }
                }
                else {
                    failIf(name2 !== "dylink.0");
                    var WASM_DYLINK_MEM_INFO = 1;
                    var WASM_DYLINK_NEEDED = 2;
                    var WASM_DYLINK_EXPORT_INFO = 3;
                    var WASM_DYLINK_IMPORT_INFO = 4;
                    var WASM_SYMBOL_TLS = 256;
                    var WASM_SYMBOL_BINDING_MASK = 3;
                    var WASM_SYMBOL_BINDING_WEAK = 1;
                    while (offset < end) {
                        var subsectionType = getU8();
                        var subsectionSize = getLEB();
                        if (subsectionType === WASM_DYLINK_MEM_INFO) {
                            customSection.memorySize = getLEB();
                            customSection.memoryAlign = getLEB();
                            customSection.tableSize = getLEB();
                            customSection.tableAlign = getLEB();
                        }
                        else if (subsectionType === WASM_DYLINK_NEEDED) {
                            var neededDynlibsCount = getLEB();
                            for (var i2 = 0; i2 < neededDynlibsCount; ++i2) {
                                libname = getString();
                                customSection.neededDynlibs.push(libname);
                            }
                        }
                        else if (subsectionType === WASM_DYLINK_EXPORT_INFO) {
                            var count = getLEB();
                            while (count--) {
                                var symname = getString();
                                var flags2 = getLEB();
                                if (flags2 & WASM_SYMBOL_TLS) {
                                    customSection.tlsExports.add(symname);
                                }
                            }
                        }
                        else if (subsectionType === WASM_DYLINK_IMPORT_INFO) {
                            var count = getLEB();
                            while (count--) {
                                var modname = getString();
                                var symname = getString();
                                var flags2 = getLEB();
                                if ((flags2 & WASM_SYMBOL_BINDING_MASK) == WASM_SYMBOL_BINDING_WEAK) {
                                    customSection.weakImports.add(symname);
                                }
                            }
                        }
                        else {
                            offset += subsectionSize;
                        }
                    }
                }
                return customSection;
            }, "getDylinkMetadata");
            function getValue(ptr, type = "i8") {
                if (type.endsWith("*"))
                    type = "*";
                switch (type) {
                    case "i1":
                        return HEAP8[ptr];
                    case "i8":
                        return HEAP8[ptr];
                    case "i16":
                        return LE_HEAP_LOAD_I16((ptr >> 1) * 2);
                    case "i32":
                        return LE_HEAP_LOAD_I32((ptr >> 2) * 4);
                    case "i64":
                        abort("to do getValue(i64) use WASM_BIGINT");
                    case "float":
                        return LE_HEAP_LOAD_F32((ptr >> 2) * 4);
                    case "double":
                        return LE_HEAP_LOAD_F64((ptr >> 3) * 8);
                    case "*":
                        return LE_HEAP_LOAD_U32((ptr >> 2) * 4);
                    default:
                        abort(`invalid type for getValue: ${type}`);
                }
            }
            __name(getValue, "getValue");
            var newDSO = /* @__PURE__ */ __name((name2, handle2, syms) => {
                var dso = {
                    refcount: Infinity,
                    name: name2,
                    exports: syms,
                    global: true
                };
                LDSO.loadedLibsByName[name2] = dso;
                if (handle2 != void 0) {
                    LDSO.loadedLibsByHandle[handle2] = dso;
                }
                return dso;
            }, "newDSO");
            var LDSO = {
                loadedLibsByName: {},
                loadedLibsByHandle: {},
                init() {
                    newDSO("__main__", 0, wasmImports);
                }
            };
            var ___heap_base = 78160;
            var zeroMemory = /* @__PURE__ */ __name((address, size) => {
                HEAPU8.fill(0, address, address + size);
                return address;
            }, "zeroMemory");
            var alignMemory = /* @__PURE__ */ __name((size, alignment) => Math.ceil(size / alignment) * alignment, "alignMemory");
            var getMemory = /* @__PURE__ */ __name((size) => {
                if (runtimeInitialized) {
                    return zeroMemory(_malloc(size), size);
                }
                var ret = ___heap_base;
                var end = ret + alignMemory(size, 16);
                ___heap_base = end;
                GOT["__heap_base"].value = end;
                return ret;
            }, "getMemory");
            var isInternalSym = /* @__PURE__ */ __name((symName) => ["__cpp_exception", "__c_longjmp", "__wasm_apply_data_relocs", "__dso_handle", "__tls_size", "__tls_align", "__set_stack_limits", "_emscripten_tls_init", "__wasm_init_tls", "__wasm_call_ctors", "__start_em_asm", "__stop_em_asm", "__start_em_js", "__stop_em_js"].includes(symName) || symName.startsWith("__em_js__"), "isInternalSym");
            var uleb128Encode = /* @__PURE__ */ __name((n, target) => {
                if (n < 128) {
                    target.push(n);
                }
                else {
                    target.push(n % 128 | 128, n >> 7);
                }
            }, "uleb128Encode");
            var sigToWasmTypes = /* @__PURE__ */ __name((sig) => {
                var typeNames = {
                    "i": "i32",
                    "j": "i64",
                    "f": "f32",
                    "d": "f64",
                    "e": "externref",
                    "p": "i32"
                };
                var type = {
                    parameters: [],
                    results: sig[0] == "v" ? [] : [typeNames[sig[0]]]
                };
                for (var i2 = 1; i2 < sig.length; ++i2) {
                    type.parameters.push(typeNames[sig[i2]]);
                }
                return type;
            }, "sigToWasmTypes");
            var generateFuncType = /* @__PURE__ */ __name((sig, target) => {
                var sigRet = sig.slice(0, 1);
                var sigParam = sig.slice(1);
                var typeCodes = {
                    "i": 127,
                    // i32
                    "p": 127,
                    // i32
                    "j": 126,
                    // i64
                    "f": 125,
                    // f32
                    "d": 124,
                    // f64
                    "e": 111
                };
                target.push(96);
                uleb128Encode(sigParam.length, target);
                for (var i2 = 0; i2 < sigParam.length; ++i2) {
                    target.push(typeCodes[sigParam[i2]]);
                }
                if (sigRet == "v") {
                    target.push(0);
                }
                else {
                    target.push(1, typeCodes[sigRet]);
                }
            }, "generateFuncType");
            var convertJsFunctionToWasm = /* @__PURE__ */ __name((func2, sig) => {
                if (typeof WebAssembly.Function == "function") {
                    return new WebAssembly.Function(sigToWasmTypes(sig), func2);
                }
                var typeSectionBody = [1];
                generateFuncType(sig, typeSectionBody);
                var bytes = [
                    0,
                    97,
                    115,
                    109,
                    // magic ("\0asm")
                    1,
                    0,
                    0,
                    0,
                    // version: 1
                    1
                ];
                uleb128Encode(typeSectionBody.length, bytes);
                bytes.push(...typeSectionBody);
                bytes.push(2, 7, 
                // import section
                // (import "e" "f" (func 0 (type 0)))
                1, 1, 101, 1, 102, 0, 0, 7, 5, 
                // export section
                // (export "f" (func 0 (type 0)))
                1, 1, 102, 0, 0);
                var module2 = new WebAssembly.Module(new Uint8Array(bytes));
                var instance2 = new WebAssembly.Instance(module2, {
                    "e": {
                        "f": func2
                    }
                });
                var wrappedFunc = instance2.exports["f"];
                return wrappedFunc;
            }, "convertJsFunctionToWasm");
            var wasmTableMirror = [];
            var wasmTable = new WebAssembly.Table({
                "initial": 31,
                "element": "anyfunc"
            });
            var getWasmTableEntry = /* @__PURE__ */ __name((funcPtr) => {
                var func2 = wasmTableMirror[funcPtr];
                if (!func2) {
                    if (funcPtr >= wasmTableMirror.length)
                        wasmTableMirror.length = funcPtr + 1;
                    wasmTableMirror[funcPtr] = func2 = wasmTable.get(funcPtr);
                }
                return func2;
            }, "getWasmTableEntry");
            var updateTableMap = /* @__PURE__ */ __name((offset, count) => {
                if (functionsInTableMap) {
                    for (var i2 = offset; i2 < offset + count; i2++) {
                        var item = getWasmTableEntry(i2);
                        if (item) {
                            functionsInTableMap.set(item, i2);
                        }
                    }
                }
            }, "updateTableMap");
            var functionsInTableMap;
            var getFunctionAddress = /* @__PURE__ */ __name((func2) => {
                if (!functionsInTableMap) {
                    functionsInTableMap = /* @__PURE__ */ new WeakMap();
                    updateTableMap(0, wasmTable.length);
                }
                return functionsInTableMap.get(func2) || 0;
            }, "getFunctionAddress");
            var freeTableIndexes = [];
            var getEmptyTableSlot = /* @__PURE__ */ __name(() => {
                if (freeTableIndexes.length) {
                    return freeTableIndexes.pop();
                }
                try {
                    wasmTable.grow(1);
                }
                catch (err2) {
                    if (!(err2 instanceof RangeError)) {
                        throw err2;
                    }
                    throw "Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.";
                }
                return wasmTable.length - 1;
            }, "getEmptyTableSlot");
            var setWasmTableEntry = /* @__PURE__ */ __name((idx, func2) => {
                wasmTable.set(idx, func2);
                wasmTableMirror[idx] = wasmTable.get(idx);
            }, "setWasmTableEntry");
            var addFunction = /* @__PURE__ */ __name((func2, sig) => {
                var rtn = getFunctionAddress(func2);
                if (rtn) {
                    return rtn;
                }
                var ret = getEmptyTableSlot();
                try {
                    setWasmTableEntry(ret, func2);
                }
                catch (err2) {
                    if (!(err2 instanceof TypeError)) {
                        throw err2;
                    }
                    var wrapped = convertJsFunctionToWasm(func2, sig);
                    setWasmTableEntry(ret, wrapped);
                }
                functionsInTableMap.set(func2, ret);
                return ret;
            }, "addFunction");
            var updateGOT = /* @__PURE__ */ __name((exports, replace) => {
                for (var symName in exports) {
                    if (isInternalSym(symName)) {
                        continue;
                    }
                    var value = exports[symName];
                    if (symName.startsWith("orig$")) {
                        symName = symName.split("$")[1];
                        replace = true;
                    }
                    GOT[symName] ||= new WebAssembly.Global({
                        "value": "i32",
                        "mutable": true
                    });
                    if (replace || GOT[symName].value == 0) {
                        if (typeof value == "function") {
                            GOT[symName].value = addFunction(value);
                        }
                        else if (typeof value == "number") {
                            GOT[symName].value = value;
                        }
                        else {
                            err(`unhandled export type for '${symName}': ${typeof value}`);
                        }
                    }
                }
            }, "updateGOT");
            var relocateExports = /* @__PURE__ */ __name((exports, memoryBase2, replace) => {
                var relocated = {};
                for (var e in exports) {
                    var value = exports[e];
                    if (typeof value == "object") {
                        value = value.value;
                    }
                    if (typeof value == "number") {
                        value += memoryBase2;
                    }
                    relocated[e] = value;
                }
                updateGOT(relocated, replace);
                return relocated;
            }, "relocateExports");
            var isSymbolDefined = /* @__PURE__ */ __name((symName) => {
                var existing = wasmImports[symName];
                if (!existing || existing.stub) {
                    return false;
                }
                return true;
            }, "isSymbolDefined");
            var dynCallLegacy = /* @__PURE__ */ __name((sig, ptr, args2) => {
                sig = sig.replace(/p/g, "i");
                var f = Module["dynCall_" + sig];
                return f(ptr, ...args2);
            }, "dynCallLegacy");
            var dynCall = /* @__PURE__ */ __name((sig, ptr, args2 = []) => {
                if (sig.includes("j")) {
                    return dynCallLegacy(sig, ptr, args2);
                }
                var rtn = getWasmTableEntry(ptr)(...args2);
                return rtn;
            }, "dynCall");
            var stackSave = /* @__PURE__ */ __name(() => _emscripten_stack_get_current(), "stackSave");
            var stackRestore = /* @__PURE__ */ __name((val) => __emscripten_stack_restore(val), "stackRestore");
            var createInvokeFunction = /* @__PURE__ */ __name((sig) => (ptr, ...args2) => {
                var sp = stackSave();
                try {
                    return dynCall(sig, ptr, args2);
                }
                catch (e) {
                    stackRestore(sp);
                    if (e !== e + 0)
                        throw e;
                    _setThrew(1, 0);
                }
            }, "createInvokeFunction");
            var resolveGlobalSymbol = /* @__PURE__ */ __name((symName, direct = false) => {
                var sym;
                if (direct && "orig$" + symName in wasmImports) {
                    symName = "orig$" + symName;
                }
                if (isSymbolDefined(symName)) {
                    sym = wasmImports[symName];
                }
                else if (symName.startsWith("invoke_")) {
                    sym = wasmImports[symName] = createInvokeFunction(symName.split("_")[1]);
                }
                return {
                    sym,
                    name: symName
                };
            }, "resolveGlobalSymbol");
            var UTF8ToString = /* @__PURE__ */ __name((ptr, maxBytesToRead) => ptr ? UTF8ArrayToString(HEAPU8, ptr, maxBytesToRead) : "", "UTF8ToString");
            var loadWebAssemblyModule = /* @__PURE__ */ __name((binary, flags, libName, localScope, handle) => {
                var metadata = getDylinkMetadata(binary);
                currentModuleWeakSymbols = metadata.weakImports;
                function loadModule() {
                    var firstLoad = !handle || !HEAP8[handle + 8];
                    if (firstLoad) {
                        var memAlign = Math.pow(2, metadata.memoryAlign);
                        var memoryBase = metadata.memorySize ? alignMemory(getMemory(metadata.memorySize + memAlign), memAlign) : 0;
                        var tableBase = metadata.tableSize ? wasmTable.length : 0;
                        if (handle) {
                            HEAP8[handle + 8] = 1;
                            LE_HEAP_STORE_U32((handle + 12 >> 2) * 4, memoryBase);
                            LE_HEAP_STORE_I32((handle + 16 >> 2) * 4, metadata.memorySize);
                            LE_HEAP_STORE_U32((handle + 20 >> 2) * 4, tableBase);
                            LE_HEAP_STORE_I32((handle + 24 >> 2) * 4, metadata.tableSize);
                        }
                    }
                    else {
                        memoryBase = LE_HEAP_LOAD_U32((handle + 12 >> 2) * 4);
                        tableBase = LE_HEAP_LOAD_U32((handle + 20 >> 2) * 4);
                    }
                    var tableGrowthNeeded = tableBase + metadata.tableSize - wasmTable.length;
                    if (tableGrowthNeeded > 0) {
                        wasmTable.grow(tableGrowthNeeded);
                    }
                    var moduleExports;
                    function resolveSymbol(sym) {
                        var resolved = resolveGlobalSymbol(sym).sym;
                        if (!resolved && localScope) {
                            resolved = localScope[sym];
                        }
                        if (!resolved) {
                            resolved = moduleExports[sym];
                        }
                        return resolved;
                    }
                    __name(resolveSymbol, "resolveSymbol");
                    var proxyHandler = {
                        get(stubs, prop) {
                            switch (prop) {
                                case "__memory_base":
                                    return memoryBase;
                                case "__table_base":
                                    return tableBase;
                            }
                            if (prop in wasmImports && !wasmImports[prop].stub) {
                                return wasmImports[prop];
                            }
                            if (!(prop in stubs)) {
                                var resolved;
                                stubs[prop] = (...args2) => {
                                    resolved ||= resolveSymbol(prop);
                                    return resolved(...args2);
                                };
                            }
                            return stubs[prop];
                        }
                    };
                    var proxy = new Proxy({}, proxyHandler);
                    var info = {
                        "GOT.mem": new Proxy({}, GOTHandler),
                        "GOT.func": new Proxy({}, GOTHandler),
                        "env": proxy,
                        "wasi_snapshot_preview1": proxy
                    };
                    function postInstantiation(module, instance) {
                        updateTableMap(tableBase, metadata.tableSize);
                        moduleExports = relocateExports(instance.exports, memoryBase);
                        if (!flags.allowUndefined) {
                            reportUndefinedSymbols();
                        }
                        function addEmAsm(addr, body) {
                            var args = [];
                            var arity = 0;
                            for (; arity < 16; arity++) {
                                if (body.indexOf("$" + arity) != -1) {
                                    args.push("$" + arity);
                                }
                                else {
                                    break;
                                }
                            }
                            args = args.join(",");
                            var func = `(${args}) => { ${body} };`;
                            ASM_CONSTS[start] = eval(func);
                        }
                        __name(addEmAsm, "addEmAsm");
                        if ("__start_em_asm" in moduleExports) {
                            var start = moduleExports["__start_em_asm"];
                            var stop = moduleExports["__stop_em_asm"];
                            while (start < stop) {
                                var jsString = UTF8ToString(start);
                                addEmAsm(start, jsString);
                                start = HEAPU8.indexOf(0, start) + 1;
                            }
                        }
                        function addEmJs(name, cSig, body) {
                            var jsArgs = [];
                            cSig = cSig.slice(1, -1);
                            if (cSig != "void") {
                                cSig = cSig.split(",");
                                for (var i in cSig) {
                                    var jsArg = cSig[i].split(" ").pop();
                                    jsArgs.push(jsArg.replace("*", ""));
                                }
                            }
                            var func = `(${jsArgs}) => ${body};`;
                            moduleExports[name] = eval(func);
                        }
                        __name(addEmJs, "addEmJs");
                        for (var name in moduleExports) {
                            if (name.startsWith("__em_js__")) {
                                var start = moduleExports[name];
                                var jsString = UTF8ToString(start);
                                var parts = jsString.split("<::>");
                                addEmJs(name.replace("__em_js__", ""), parts[0], parts[1]);
                                delete moduleExports[name];
                            }
                        }
                        var applyRelocs = moduleExports["__wasm_apply_data_relocs"];
                        if (applyRelocs) {
                            if (runtimeInitialized) {
                                applyRelocs();
                            }
                            else {
                                __RELOC_FUNCS__.push(applyRelocs);
                            }
                        }
                        var init = moduleExports["__wasm_call_ctors"];
                        if (init) {
                            if (runtimeInitialized) {
                                init();
                            }
                            else {
                                __ATINIT__.push(init);
                            }
                        }
                        return moduleExports;
                    }
                    __name(postInstantiation, "postInstantiation");
                    if (flags.loadAsync) {
                        if (binary instanceof WebAssembly.Module) {
                            var instance = new WebAssembly.Instance(binary, info);
                            return Promise.resolve(postInstantiation(binary, instance));
                        }
                        return WebAssembly.instantiate(binary, info).then((result) => postInstantiation(result.module, result.instance));
                    }
                    var module = binary instanceof WebAssembly.Module ? binary : new WebAssembly.Module(binary);
                    var instance = new WebAssembly.Instance(module, info);
                    return postInstantiation(module, instance);
                }
                __name(loadModule, "loadModule");
                if (flags.loadAsync) {
                    return metadata.neededDynlibs.reduce((chain, dynNeeded) => chain.then(() => loadDynamicLibrary(dynNeeded, flags, localScope)), Promise.resolve()).then(loadModule);
                }
                metadata.neededDynlibs.forEach((needed) => loadDynamicLibrary(needed, flags, localScope));
                return loadModule();
            }, "loadWebAssemblyModule");
            var mergeLibSymbols = /* @__PURE__ */ __name((exports, libName2) => {
                for (var [sym, exp] of Object.entries(exports)) {
                    const setImport = /* @__PURE__ */ __name((target) => {
                        if (!isSymbolDefined(target)) {
                            wasmImports[target] = exp;
                        }
                    }, "setImport");
                    setImport(sym);
                    const main_alias = "__main_argc_argv";
                    if (sym == "main") {
                        setImport(main_alias);
                    }
                    if (sym == main_alias) {
                        setImport("main");
                    }
                    if (sym.startsWith("dynCall_") && !Module.hasOwnProperty(sym)) {
                        Module[sym] = exp;
                    }
                }
            }, "mergeLibSymbols");
            var asyncLoad = /* @__PURE__ */ __name((url, onload, onerror, noRunDep) => {
                var dep = !noRunDep ? getUniqueRunDependency(`al ${url}`) : "";
                readAsync(url).then((arrayBuffer) => {
                    onload(new Uint8Array(arrayBuffer));
                    if (dep)
                        removeRunDependency(dep);
                }, (err2) => {
                    if (onerror) {
                        onerror();
                    }
                    else {
                        throw `Loading data file "${url}" failed.`;
                    }
                });
                if (dep)
                    addRunDependency(dep);
            }, "asyncLoad");
            function loadDynamicLibrary(libName2, flags2 = {
                global: true,
                nodelete: true
            }, localScope2, handle2) {
                var dso = LDSO.loadedLibsByName[libName2];
                if (dso) {
                    if (!flags2.global) {
                        if (localScope2) {
                            Object.assign(localScope2, dso.exports);
                        }
                    }
                    else if (!dso.global) {
                        dso.global = true;
                        mergeLibSymbols(dso.exports, libName2);
                    }
                    if (flags2.nodelete && dso.refcount !== Infinity) {
                        dso.refcount = Infinity;
                    }
                    dso.refcount++;
                    if (handle2) {
                        LDSO.loadedLibsByHandle[handle2] = dso;
                    }
                    return flags2.loadAsync ? Promise.resolve(true) : true;
                }
                dso = newDSO(libName2, handle2, "loading");
                dso.refcount = flags2.nodelete ? Infinity : 1;
                dso.global = flags2.global;
                function loadLibData() {
                    if (handle2) {
                        var data = LE_HEAP_LOAD_U32((handle2 + 28 >> 2) * 4);
                        var dataSize = LE_HEAP_LOAD_U32((handle2 + 32 >> 2) * 4);
                        if (data && dataSize) {
                            var libData = HEAP8.slice(data, data + dataSize);
                            return flags2.loadAsync ? Promise.resolve(libData) : libData;
                        }
                    }
                    var libFile = locateFile(libName2);
                    if (flags2.loadAsync) {
                        return new Promise(function (resolve, reject) {
                            asyncLoad(libFile, resolve, reject);
                        });
                    }
                    if (!readBinary) {
                        throw new Error(`${libFile}: file not found, and synchronous loading of external files is not available`);
                    }
                    return readBinary(libFile);
                }
                __name(loadLibData, "loadLibData");
                function getExports() {
                    if (flags2.loadAsync) {
                        return loadLibData().then((libData) => loadWebAssemblyModule(libData, flags2, libName2, localScope2, handle2));
                    }
                    return loadWebAssemblyModule(loadLibData(), flags2, libName2, localScope2, handle2);
                }
                __name(getExports, "getExports");
                function moduleLoaded(exports) {
                    if (dso.global) {
                        mergeLibSymbols(exports, libName2);
                    }
                    else if (localScope2) {
                        Object.assign(localScope2, exports);
                    }
                    dso.exports = exports;
                }
                __name(moduleLoaded, "moduleLoaded");
                if (flags2.loadAsync) {
                    return getExports().then((exports) => {
                        moduleLoaded(exports);
                        return true;
                    });
                }
                moduleLoaded(getExports());
                return true;
            }
            __name(loadDynamicLibrary, "loadDynamicLibrary");
            var reportUndefinedSymbols = /* @__PURE__ */ __name(() => {
                for (var [symName, entry] of Object.entries(GOT)) {
                    if (entry.value == 0) {
                        var value = resolveGlobalSymbol(symName, true).sym;
                        if (!value && !entry.required) {
                            continue;
                        }
                        if (typeof value == "function") {
                            entry.value = addFunction(value, value.sig);
                        }
                        else if (typeof value == "number") {
                            entry.value = value;
                        }
                        else {
                            throw new Error(`bad export type for '${symName}': ${typeof value}`);
                        }
                    }
                }
            }, "reportUndefinedSymbols");
            var loadDylibs = /* @__PURE__ */ __name(() => {
                if (!dynamicLibraries.length) {
                    reportUndefinedSymbols();
                    return;
                }
                addRunDependency("loadDylibs");
                dynamicLibraries.reduce((chain, lib) => chain.then(() => loadDynamicLibrary(lib, {
                    loadAsync: true,
                    global: true,
                    nodelete: true,
                    allowUndefined: true
                })), Promise.resolve()).then(() => {
                    reportUndefinedSymbols();
                    removeRunDependency("loadDylibs");
                });
            }, "loadDylibs");
            var noExitRuntime = Module["noExitRuntime"] || true;
            function setValue(ptr, value, type = "i8") {
                if (type.endsWith("*"))
                    type = "*";
                switch (type) {
                    case "i1":
                        HEAP8[ptr] = value;
                        break;
                    case "i8":
                        HEAP8[ptr] = value;
                        break;
                    case "i16":
                        LE_HEAP_STORE_I16((ptr >> 1) * 2, value);
                        break;
                    case "i32":
                        LE_HEAP_STORE_I32((ptr >> 2) * 4, value);
                        break;
                    case "i64":
                        abort("to do setValue(i64) use WASM_BIGINT");
                    case "float":
                        LE_HEAP_STORE_F32((ptr >> 2) * 4, value);
                        break;
                    case "double":
                        LE_HEAP_STORE_F64((ptr >> 3) * 8, value);
                        break;
                    case "*":
                        LE_HEAP_STORE_U32((ptr >> 2) * 4, value);
                        break;
                    default:
                        abort(`invalid type for setValue: ${type}`);
                }
            }
            __name(setValue, "setValue");
            var ___memory_base = new WebAssembly.Global({
                "value": "i32",
                "mutable": false
            }, 1024);
            var ___stack_pointer = new WebAssembly.Global({
                "value": "i32",
                "mutable": true
            }, 78160);
            var ___table_base = new WebAssembly.Global({
                "value": "i32",
                "mutable": false
            }, 1);
            var __abort_js = /* @__PURE__ */ __name(() => {
                abort("");
            }, "__abort_js");
            __abort_js.sig = "v";
            var nowIsMonotonic = 1;
            var __emscripten_get_now_is_monotonic = /* @__PURE__ */ __name(() => nowIsMonotonic, "__emscripten_get_now_is_monotonic");
            __emscripten_get_now_is_monotonic.sig = "i";
            var __emscripten_memcpy_js = /* @__PURE__ */ __name((dest, src, num) => HEAPU8.copyWithin(dest, src, src + num), "__emscripten_memcpy_js");
            __emscripten_memcpy_js.sig = "vppp";
            var _emscripten_date_now = /* @__PURE__ */ __name(() => Date.now(), "_emscripten_date_now");
            _emscripten_date_now.sig = "d";
            var _emscripten_get_now;
            _emscripten_get_now = /* @__PURE__ */ __name(() => performance.now(), "_emscripten_get_now");
            _emscripten_get_now.sig = "d";
            var getHeapMax = /* @__PURE__ */ __name(() => (
            // Stay one Wasm page short of 4GB: while e.g. Chrome is able to allocate
            // full 4GB Wasm memories, the size will wrap back to 0 bytes in Wasm side
            // for any code that deals with heap sizes, which would require special
            // casing all heap size related code to treat 0 specially.
            2147483648), "getHeapMax");
            var growMemory = /* @__PURE__ */ __name((size) => {
                var b = wasmMemory.buffer;
                var pages = (size - b.byteLength + 65535) / 65536;
                try {
                    wasmMemory.grow(pages);
                    updateMemoryViews();
                    return 1;
                }
                catch (e) {
                }
            }, "growMemory");
            var _emscripten_resize_heap = /* @__PURE__ */ __name((requestedSize) => {
                var oldSize = HEAPU8.length;
                requestedSize >>>= 0;
                var maxHeapSize = getHeapMax();
                if (requestedSize > maxHeapSize) {
                    return false;
                }
                var alignUp = /* @__PURE__ */ __name((x, multiple) => x + (multiple - x % multiple) % multiple, "alignUp");
                for (var cutDown = 1; cutDown <= 4; cutDown *= 2) {
                    var overGrownHeapSize = oldSize * (1 + 0.2 / cutDown);
                    overGrownHeapSize = Math.min(overGrownHeapSize, requestedSize + 100663296);
                    var newSize = Math.min(maxHeapSize, alignUp(Math.max(requestedSize, overGrownHeapSize), 65536));
                    var replacement = growMemory(newSize);
                    if (replacement) {
                        return true;
                    }
                }
                return false;
            }, "_emscripten_resize_heap");
            _emscripten_resize_heap.sig = "ip";
            var _fd_close = /* @__PURE__ */ __name((fd) => 52, "_fd_close");
            _fd_close.sig = "ii";
            var convertI32PairToI53Checked = /* @__PURE__ */ __name((lo, hi) => hi + 2097152 >>> 0 < 4194305 - !!lo ? (lo >>> 0) + hi * 4294967296 : NaN, "convertI32PairToI53Checked");
            function _fd_seek(fd, offset_low, offset_high, whence, newOffset) {
                var offset = convertI32PairToI53Checked(offset_low, offset_high);
                return 70;
            }
            __name(_fd_seek, "_fd_seek");
            _fd_seek.sig = "iiiiip";
            var printCharBuffers = [null, [], []];
            var printChar = /* @__PURE__ */ __name((stream, curr) => {
                var buffer = printCharBuffers[stream];
                if (curr === 0 || curr === 10) {
                    (stream === 1 ? out : err)(UTF8ArrayToString(buffer, 0));
                    buffer.length = 0;
                }
                else {
                    buffer.push(curr);
                }
            }, "printChar");
            var flush_NO_FILESYSTEM = /* @__PURE__ */ __name(() => {
                if (printCharBuffers[1].length)
                    printChar(1, 10);
                if (printCharBuffers[2].length)
                    printChar(2, 10);
            }, "flush_NO_FILESYSTEM");
            var SYSCALLS = {
                varargs: void 0,
                getStr(ptr) {
                    var ret = UTF8ToString(ptr);
                    return ret;
                }
            };
            var _fd_write = /* @__PURE__ */ __name((fd, iov, iovcnt, pnum) => {
                var num = 0;
                for (var i2 = 0; i2 < iovcnt; i2++) {
                    var ptr = LE_HEAP_LOAD_U32((iov >> 2) * 4);
                    var len = LE_HEAP_LOAD_U32((iov + 4 >> 2) * 4);
                    iov += 8;
                    for (var j = 0; j < len; j++) {
                        printChar(fd, HEAPU8[ptr + j]);
                    }
                    num += len;
                }
                LE_HEAP_STORE_U32((pnum >> 2) * 4, num);
                return 0;
            }, "_fd_write");
            _fd_write.sig = "iippp";
            function _tree_sitter_log_callback(isLexMessage, messageAddress) {
                if (Module.currentLogCallback) {
                    const message = UTF8ToString(messageAddress);
                    Module.currentLogCallback(message, isLexMessage !== 0);
                }
            }
            __name(_tree_sitter_log_callback, "_tree_sitter_log_callback");
            function _tree_sitter_parse_callback(inputBufferAddress, index, row, column, lengthAddress) {
                const INPUT_BUFFER_SIZE = 10 * 1024;
                const string = Module.currentParseCallback(index, {
                    row,
                    column
                });
                if (typeof string === "string") {
                    setValue(lengthAddress, string.length, "i32");
                    stringToUTF16(string, inputBufferAddress, INPUT_BUFFER_SIZE);
                }
                else {
                    setValue(lengthAddress, 0, "i32");
                }
            }
            __name(_tree_sitter_parse_callback, "_tree_sitter_parse_callback");
            function _tree_sitter_progress_callback(currentOffset, hasError) {
                if (Module.currentProgressCallback) {
                    return Module.currentProgressCallback({
                        currentOffset,
                        hasError
                    });
                }
                return false;
            }
            __name(_tree_sitter_progress_callback, "_tree_sitter_progress_callback");
            function _tree_sitter_query_progress_callback(currentOffset) {
                if (Module.currentQueryProgressCallback) {
                    return Module.currentQueryProgressCallback({
                        currentOffset
                    });
                }
                return false;
            }
            __name(_tree_sitter_query_progress_callback, "_tree_sitter_query_progress_callback");
            var runtimeKeepaliveCounter = 0;
            var keepRuntimeAlive = /* @__PURE__ */ __name(() => noExitRuntime || runtimeKeepaliveCounter > 0, "keepRuntimeAlive");
            var _proc_exit = /* @__PURE__ */ __name((code) => {
                EXITSTATUS = code;
                if (!keepRuntimeAlive()) {
                    Module["onExit"]?.(code);
                    ABORT = true;
                }
                quit_(code, new ExitStatus(code));
            }, "_proc_exit");
            _proc_exit.sig = "vi";
            var exitJS = /* @__PURE__ */ __name((status, implicit) => {
                EXITSTATUS = status;
                _proc_exit(status);
            }, "exitJS");
            var handleException = /* @__PURE__ */ __name((e) => {
                if (e instanceof ExitStatus || e == "unwind") {
                    return EXITSTATUS;
                }
                quit_(1, e);
            }, "handleException");
            var lengthBytesUTF8 = /* @__PURE__ */ __name((str) => {
                var len = 0;
                for (var i2 = 0; i2 < str.length; ++i2) {
                    var c = str.charCodeAt(i2);
                    if (c <= 127) {
                        len++;
                    }
                    else if (c <= 2047) {
                        len += 2;
                    }
                    else if (c >= 55296 && c <= 57343) {
                        len += 4;
                        ++i2;
                    }
                    else {
                        len += 3;
                    }
                }
                return len;
            }, "lengthBytesUTF8");
            var stringToUTF8Array = /* @__PURE__ */ __name((str, heap, outIdx, maxBytesToWrite) => {
                if (!(maxBytesToWrite > 0))
                    return 0;
                var startIdx = outIdx;
                var endIdx = outIdx + maxBytesToWrite - 1;
                for (var i2 = 0; i2 < str.length; ++i2) {
                    var u = str.charCodeAt(i2);
                    if (u >= 55296 && u <= 57343) {
                        var u1 = str.charCodeAt(++i2);
                        u = 65536 + ((u & 1023) << 10) | u1 & 1023;
                    }
                    if (u <= 127) {
                        if (outIdx >= endIdx)
                            break;
                        heap[outIdx++] = u;
                    }
                    else if (u <= 2047) {
                        if (outIdx + 1 >= endIdx)
                            break;
                        heap[outIdx++] = 192 | u >> 6;
                        heap[outIdx++] = 128 | u & 63;
                    }
                    else if (u <= 65535) {
                        if (outIdx + 2 >= endIdx)
                            break;
                        heap[outIdx++] = 224 | u >> 12;
                        heap[outIdx++] = 128 | u >> 6 & 63;
                        heap[outIdx++] = 128 | u & 63;
                    }
                    else {
                        if (outIdx + 3 >= endIdx)
                            break;
                        heap[outIdx++] = 240 | u >> 18;
                        heap[outIdx++] = 128 | u >> 12 & 63;
                        heap[outIdx++] = 128 | u >> 6 & 63;
                        heap[outIdx++] = 128 | u & 63;
                    }
                }
                heap[outIdx] = 0;
                return outIdx - startIdx;
            }, "stringToUTF8Array");
            var stringToUTF8 = /* @__PURE__ */ __name((str, outPtr, maxBytesToWrite) => stringToUTF8Array(str, HEAPU8, outPtr, maxBytesToWrite), "stringToUTF8");
            var stackAlloc = /* @__PURE__ */ __name((sz) => __emscripten_stack_alloc(sz), "stackAlloc");
            var stringToUTF8OnStack = /* @__PURE__ */ __name((str) => {
                var size = lengthBytesUTF8(str) + 1;
                var ret = stackAlloc(size);
                stringToUTF8(str, ret, size);
                return ret;
            }, "stringToUTF8OnStack");
            var AsciiToString = /* @__PURE__ */ __name((ptr) => {
                var str = "";
                while (1) {
                    var ch = HEAPU8[ptr++];
                    if (!ch)
                        return str;
                    str += String.fromCharCode(ch);
                }
            }, "AsciiToString");
            var stringToUTF16 = /* @__PURE__ */ __name((str, outPtr, maxBytesToWrite) => {
                maxBytesToWrite ??= 2147483647;
                if (maxBytesToWrite < 2)
                    return 0;
                maxBytesToWrite -= 2;
                var startPtr = outPtr;
                var numCharsToWrite = maxBytesToWrite < str.length * 2 ? maxBytesToWrite / 2 : str.length;
                for (var i2 = 0; i2 < numCharsToWrite; ++i2) {
                    var codeUnit = str.charCodeAt(i2);
                    LE_HEAP_STORE_I16((outPtr >> 1) * 2, codeUnit);
                    outPtr += 2;
                }
                LE_HEAP_STORE_I16((outPtr >> 1) * 2, 0);
                return outPtr - startPtr;
            }, "stringToUTF16");
            var wasmImports = {
                /** @export */
                __heap_base: ___heap_base,
                /** @export */
                __indirect_function_table: wasmTable,
                /** @export */
                __memory_base: ___memory_base,
                /** @export */
                __stack_pointer: ___stack_pointer,
                /** @export */
                __table_base: ___table_base,
                /** @export */
                _abort_js: __abort_js,
                /** @export */
                _emscripten_get_now_is_monotonic: __emscripten_get_now_is_monotonic,
                /** @export */
                _emscripten_memcpy_js: __emscripten_memcpy_js,
                /** @export */
                emscripten_date_now: _emscripten_date_now,
                /** @export */
                emscripten_get_now: _emscripten_get_now,
                /** @export */
                emscripten_resize_heap: _emscripten_resize_heap,
                /** @export */
                fd_close: _fd_close,
                /** @export */
                fd_seek: _fd_seek,
                /** @export */
                fd_write: _fd_write,
                /** @export */
                memory: wasmMemory,
                /** @export */
                tree_sitter_log_callback: _tree_sitter_log_callback,
                /** @export */
                tree_sitter_parse_callback: _tree_sitter_parse_callback,
                /** @export */
                tree_sitter_progress_callback: _tree_sitter_progress_callback,
                /** @export */
                tree_sitter_query_progress_callback: _tree_sitter_query_progress_callback
            };
            var wasmExports = createWasm();
            var ___wasm_call_ctors = /* @__PURE__ */ __name(() => (___wasm_call_ctors = wasmExports["__wasm_call_ctors"])(), "___wasm_call_ctors");
            var ___wasm_apply_data_relocs = /* @__PURE__ */ __name(() => (___wasm_apply_data_relocs = wasmExports["__wasm_apply_data_relocs"])(), "___wasm_apply_data_relocs");
            var _malloc = Module["_malloc"] = (a0) => (_malloc = Module["_malloc"] = wasmExports["malloc"])(a0);
            var _calloc = Module["_calloc"] = (a0, a1) => (_calloc = Module["_calloc"] = wasmExports["calloc"])(a0, a1);
            var _realloc = Module["_realloc"] = (a0, a1) => (_realloc = Module["_realloc"] = wasmExports["realloc"])(a0, a1);
            var _free = Module["_free"] = (a0) => (_free = Module["_free"] = wasmExports["free"])(a0);
            var _ts_language_symbol_count = Module["_ts_language_symbol_count"] = (a0) => (_ts_language_symbol_count = Module["_ts_language_symbol_count"] = wasmExports["ts_language_symbol_count"])(a0);
            var _ts_language_state_count = Module["_ts_language_state_count"] = (a0) => (_ts_language_state_count = Module["_ts_language_state_count"] = wasmExports["ts_language_state_count"])(a0);
            var _ts_language_version = Module["_ts_language_version"] = (a0) => (_ts_language_version = Module["_ts_language_version"] = wasmExports["ts_language_version"])(a0);
            var _ts_language_abi_version = Module["_ts_language_abi_version"] = (a0) => (_ts_language_abi_version = Module["_ts_language_abi_version"] = wasmExports["ts_language_abi_version"])(a0);
            var _ts_language_metadata = Module["_ts_language_metadata"] = (a0) => (_ts_language_metadata = Module["_ts_language_metadata"] = wasmExports["ts_language_metadata"])(a0);
            var _ts_language_name = Module["_ts_language_name"] = (a0) => (_ts_language_name = Module["_ts_language_name"] = wasmExports["ts_language_name"])(a0);
            var _ts_language_field_count = Module["_ts_language_field_count"] = (a0) => (_ts_language_field_count = Module["_ts_language_field_count"] = wasmExports["ts_language_field_count"])(a0);
            var _ts_language_next_state = Module["_ts_language_next_state"] = (a0, a1, a2) => (_ts_language_next_state = Module["_ts_language_next_state"] = wasmExports["ts_language_next_state"])(a0, a1, a2);
            var _ts_language_symbol_name = Module["_ts_language_symbol_name"] = (a0, a1) => (_ts_language_symbol_name = Module["_ts_language_symbol_name"] = wasmExports["ts_language_symbol_name"])(a0, a1);
            var _ts_language_symbol_for_name = Module["_ts_language_symbol_for_name"] = (a0, a1, a2, a3) => (_ts_language_symbol_for_name = Module["_ts_language_symbol_for_name"] = wasmExports["ts_language_symbol_for_name"])(a0, a1, a2, a3);
            var _strncmp = Module["_strncmp"] = (a0, a1, a2) => (_strncmp = Module["_strncmp"] = wasmExports["strncmp"])(a0, a1, a2);
            var _ts_language_symbol_type = Module["_ts_language_symbol_type"] = (a0, a1) => (_ts_language_symbol_type = Module["_ts_language_symbol_type"] = wasmExports["ts_language_symbol_type"])(a0, a1);
            var _ts_language_field_name_for_id = Module["_ts_language_field_name_for_id"] = (a0, a1) => (_ts_language_field_name_for_id = Module["_ts_language_field_name_for_id"] = wasmExports["ts_language_field_name_for_id"])(a0, a1);
            var _ts_lookahead_iterator_new = Module["_ts_lookahead_iterator_new"] = (a0, a1) => (_ts_lookahead_iterator_new = Module["_ts_lookahead_iterator_new"] = wasmExports["ts_lookahead_iterator_new"])(a0, a1);
            var _ts_lookahead_iterator_delete = Module["_ts_lookahead_iterator_delete"] = (a0) => (_ts_lookahead_iterator_delete = Module["_ts_lookahead_iterator_delete"] = wasmExports["ts_lookahead_iterator_delete"])(a0);
            var _ts_lookahead_iterator_reset_state = Module["_ts_lookahead_iterator_reset_state"] = (a0, a1) => (_ts_lookahead_iterator_reset_state = Module["_ts_lookahead_iterator_reset_state"] = wasmExports["ts_lookahead_iterator_reset_state"])(a0, a1);
            var _ts_lookahead_iterator_reset = Module["_ts_lookahead_iterator_reset"] = (a0, a1, a2) => (_ts_lookahead_iterator_reset = Module["_ts_lookahead_iterator_reset"] = wasmExports["ts_lookahead_iterator_reset"])(a0, a1, a2);
            var _ts_lookahead_iterator_next = Module["_ts_lookahead_iterator_next"] = (a0) => (_ts_lookahead_iterator_next = Module["_ts_lookahead_iterator_next"] = wasmExports["ts_lookahead_iterator_next"])(a0);
            var _ts_lookahead_iterator_current_symbol = Module["_ts_lookahead_iterator_current_symbol"] = (a0) => (_ts_lookahead_iterator_current_symbol = Module["_ts_lookahead_iterator_current_symbol"] = wasmExports["ts_lookahead_iterator_current_symbol"])(a0);
            var _memset = Module["_memset"] = (a0, a1, a2) => (_memset = Module["_memset"] = wasmExports["memset"])(a0, a1, a2);
            var _memcpy = Module["_memcpy"] = (a0, a1, a2) => (_memcpy = Module["_memcpy"] = wasmExports["memcpy"])(a0, a1, a2);
            var _ts_parser_delete = Module["_ts_parser_delete"] = (a0) => (_ts_parser_delete = Module["_ts_parser_delete"] = wasmExports["ts_parser_delete"])(a0);
            var _ts_parser_reset = Module["_ts_parser_reset"] = (a0) => (_ts_parser_reset = Module["_ts_parser_reset"] = wasmExports["ts_parser_reset"])(a0);
            var _ts_parser_set_language = Module["_ts_parser_set_language"] = (a0, a1) => (_ts_parser_set_language = Module["_ts_parser_set_language"] = wasmExports["ts_parser_set_language"])(a0, a1);
            var _ts_parser_timeout_micros = Module["_ts_parser_timeout_micros"] = (a0) => (_ts_parser_timeout_micros = Module["_ts_parser_timeout_micros"] = wasmExports["ts_parser_timeout_micros"])(a0);
            var _ts_parser_set_timeout_micros = Module["_ts_parser_set_timeout_micros"] = (a0, a1, a2) => (_ts_parser_set_timeout_micros = Module["_ts_parser_set_timeout_micros"] = wasmExports["ts_parser_set_timeout_micros"])(a0, a1, a2);
            var _ts_parser_set_included_ranges = Module["_ts_parser_set_included_ranges"] = (a0, a1, a2) => (_ts_parser_set_included_ranges = Module["_ts_parser_set_included_ranges"] = wasmExports["ts_parser_set_included_ranges"])(a0, a1, a2);
            var _memmove = Module["_memmove"] = (a0, a1, a2) => (_memmove = Module["_memmove"] = wasmExports["memmove"])(a0, a1, a2);
            var _memcmp = Module["_memcmp"] = (a0, a1, a2) => (_memcmp = Module["_memcmp"] = wasmExports["memcmp"])(a0, a1, a2);
            var _ts_query_new = Module["_ts_query_new"] = (a0, a1, a2, a3, a4) => (_ts_query_new = Module["_ts_query_new"] = wasmExports["ts_query_new"])(a0, a1, a2, a3, a4);
            var _ts_query_delete = Module["_ts_query_delete"] = (a0) => (_ts_query_delete = Module["_ts_query_delete"] = wasmExports["ts_query_delete"])(a0);
            var _iswspace = Module["_iswspace"] = (a0) => (_iswspace = Module["_iswspace"] = wasmExports["iswspace"])(a0);
            var _iswalnum = Module["_iswalnum"] = (a0) => (_iswalnum = Module["_iswalnum"] = wasmExports["iswalnum"])(a0);
            var _ts_query_pattern_count = Module["_ts_query_pattern_count"] = (a0) => (_ts_query_pattern_count = Module["_ts_query_pattern_count"] = wasmExports["ts_query_pattern_count"])(a0);
            var _ts_query_capture_count = Module["_ts_query_capture_count"] = (a0) => (_ts_query_capture_count = Module["_ts_query_capture_count"] = wasmExports["ts_query_capture_count"])(a0);
            var _ts_query_string_count = Module["_ts_query_string_count"] = (a0) => (_ts_query_string_count = Module["_ts_query_string_count"] = wasmExports["ts_query_string_count"])(a0);
            var _ts_query_capture_name_for_id = Module["_ts_query_capture_name_for_id"] = (a0, a1, a2) => (_ts_query_capture_name_for_id = Module["_ts_query_capture_name_for_id"] = wasmExports["ts_query_capture_name_for_id"])(a0, a1, a2);
            var _ts_query_capture_quantifier_for_id = Module["_ts_query_capture_quantifier_for_id"] = (a0, a1, a2) => (_ts_query_capture_quantifier_for_id = Module["_ts_query_capture_quantifier_for_id"] = wasmExports["ts_query_capture_quantifier_for_id"])(a0, a1, a2);
            var _ts_query_string_value_for_id = Module["_ts_query_string_value_for_id"] = (a0, a1, a2) => (_ts_query_string_value_for_id = Module["_ts_query_string_value_for_id"] = wasmExports["ts_query_string_value_for_id"])(a0, a1, a2);
            var _ts_query_predicates_for_pattern = Module["_ts_query_predicates_for_pattern"] = (a0, a1, a2) => (_ts_query_predicates_for_pattern = Module["_ts_query_predicates_for_pattern"] = wasmExports["ts_query_predicates_for_pattern"])(a0, a1, a2);
            var _ts_query_start_byte_for_pattern = Module["_ts_query_start_byte_for_pattern"] = (a0, a1) => (_ts_query_start_byte_for_pattern = Module["_ts_query_start_byte_for_pattern"] = wasmExports["ts_query_start_byte_for_pattern"])(a0, a1);
            var _ts_query_end_byte_for_pattern = Module["_ts_query_end_byte_for_pattern"] = (a0, a1) => (_ts_query_end_byte_for_pattern = Module["_ts_query_end_byte_for_pattern"] = wasmExports["ts_query_end_byte_for_pattern"])(a0, a1);
            var _ts_query_is_pattern_rooted = Module["_ts_query_is_pattern_rooted"] = (a0, a1) => (_ts_query_is_pattern_rooted = Module["_ts_query_is_pattern_rooted"] = wasmExports["ts_query_is_pattern_rooted"])(a0, a1);
            var _ts_query_is_pattern_non_local = Module["_ts_query_is_pattern_non_local"] = (a0, a1) => (_ts_query_is_pattern_non_local = Module["_ts_query_is_pattern_non_local"] = wasmExports["ts_query_is_pattern_non_local"])(a0, a1);
            var _ts_query_is_pattern_guaranteed_at_step = Module["_ts_query_is_pattern_guaranteed_at_step"] = (a0, a1) => (_ts_query_is_pattern_guaranteed_at_step = Module["_ts_query_is_pattern_guaranteed_at_step"] = wasmExports["ts_query_is_pattern_guaranteed_at_step"])(a0, a1);
            var _ts_query_disable_capture = Module["_ts_query_disable_capture"] = (a0, a1, a2) => (_ts_query_disable_capture = Module["_ts_query_disable_capture"] = wasmExports["ts_query_disable_capture"])(a0, a1, a2);
            var _ts_query_disable_pattern = Module["_ts_query_disable_pattern"] = (a0, a1) => (_ts_query_disable_pattern = Module["_ts_query_disable_pattern"] = wasmExports["ts_query_disable_pattern"])(a0, a1);
            var _ts_tree_copy = Module["_ts_tree_copy"] = (a0) => (_ts_tree_copy = Module["_ts_tree_copy"] = wasmExports["ts_tree_copy"])(a0);
            var _ts_tree_delete = Module["_ts_tree_delete"] = (a0) => (_ts_tree_delete = Module["_ts_tree_delete"] = wasmExports["ts_tree_delete"])(a0);
            var _ts_init = Module["_ts_init"] = () => (_ts_init = Module["_ts_init"] = wasmExports["ts_init"])();
            var _ts_parser_new_wasm = Module["_ts_parser_new_wasm"] = () => (_ts_parser_new_wasm = Module["_ts_parser_new_wasm"] = wasmExports["ts_parser_new_wasm"])();
            var _ts_parser_enable_logger_wasm = Module["_ts_parser_enable_logger_wasm"] = (a0, a1) => (_ts_parser_enable_logger_wasm = Module["_ts_parser_enable_logger_wasm"] = wasmExports["ts_parser_enable_logger_wasm"])(a0, a1);
            var _ts_parser_parse_wasm = Module["_ts_parser_parse_wasm"] = (a0, a1, a2, a3, a4) => (_ts_parser_parse_wasm = Module["_ts_parser_parse_wasm"] = wasmExports["ts_parser_parse_wasm"])(a0, a1, a2, a3, a4);
            var _ts_parser_included_ranges_wasm = Module["_ts_parser_included_ranges_wasm"] = (a0) => (_ts_parser_included_ranges_wasm = Module["_ts_parser_included_ranges_wasm"] = wasmExports["ts_parser_included_ranges_wasm"])(a0);
            var _ts_language_type_is_named_wasm = Module["_ts_language_type_is_named_wasm"] = (a0, a1) => (_ts_language_type_is_named_wasm = Module["_ts_language_type_is_named_wasm"] = wasmExports["ts_language_type_is_named_wasm"])(a0, a1);
            var _ts_language_type_is_visible_wasm = Module["_ts_language_type_is_visible_wasm"] = (a0, a1) => (_ts_language_type_is_visible_wasm = Module["_ts_language_type_is_visible_wasm"] = wasmExports["ts_language_type_is_visible_wasm"])(a0, a1);
            var _ts_language_supertypes_wasm = Module["_ts_language_supertypes_wasm"] = (a0) => (_ts_language_supertypes_wasm = Module["_ts_language_supertypes_wasm"] = wasmExports["ts_language_supertypes_wasm"])(a0);
            var _ts_language_subtypes_wasm = Module["_ts_language_subtypes_wasm"] = (a0, a1) => (_ts_language_subtypes_wasm = Module["_ts_language_subtypes_wasm"] = wasmExports["ts_language_subtypes_wasm"])(a0, a1);
            var _ts_tree_root_node_wasm = Module["_ts_tree_root_node_wasm"] = (a0) => (_ts_tree_root_node_wasm = Module["_ts_tree_root_node_wasm"] = wasmExports["ts_tree_root_node_wasm"])(a0);
            var _ts_tree_root_node_with_offset_wasm = Module["_ts_tree_root_node_with_offset_wasm"] = (a0) => (_ts_tree_root_node_with_offset_wasm = Module["_ts_tree_root_node_with_offset_wasm"] = wasmExports["ts_tree_root_node_with_offset_wasm"])(a0);
            var _ts_tree_edit_wasm = Module["_ts_tree_edit_wasm"] = (a0) => (_ts_tree_edit_wasm = Module["_ts_tree_edit_wasm"] = wasmExports["ts_tree_edit_wasm"])(a0);
            var _ts_tree_included_ranges_wasm = Module["_ts_tree_included_ranges_wasm"] = (a0) => (_ts_tree_included_ranges_wasm = Module["_ts_tree_included_ranges_wasm"] = wasmExports["ts_tree_included_ranges_wasm"])(a0);
            var _ts_tree_get_changed_ranges_wasm = Module["_ts_tree_get_changed_ranges_wasm"] = (a0, a1) => (_ts_tree_get_changed_ranges_wasm = Module["_ts_tree_get_changed_ranges_wasm"] = wasmExports["ts_tree_get_changed_ranges_wasm"])(a0, a1);
            var _ts_tree_cursor_new_wasm = Module["_ts_tree_cursor_new_wasm"] = (a0) => (_ts_tree_cursor_new_wasm = Module["_ts_tree_cursor_new_wasm"] = wasmExports["ts_tree_cursor_new_wasm"])(a0);
            var _ts_tree_cursor_copy_wasm = Module["_ts_tree_cursor_copy_wasm"] = (a0) => (_ts_tree_cursor_copy_wasm = Module["_ts_tree_cursor_copy_wasm"] = wasmExports["ts_tree_cursor_copy_wasm"])(a0);
            var _ts_tree_cursor_delete_wasm = Module["_ts_tree_cursor_delete_wasm"] = (a0) => (_ts_tree_cursor_delete_wasm = Module["_ts_tree_cursor_delete_wasm"] = wasmExports["ts_tree_cursor_delete_wasm"])(a0);
            var _ts_tree_cursor_reset_wasm = Module["_ts_tree_cursor_reset_wasm"] = (a0) => (_ts_tree_cursor_reset_wasm = Module["_ts_tree_cursor_reset_wasm"] = wasmExports["ts_tree_cursor_reset_wasm"])(a0);
            var _ts_tree_cursor_reset_to_wasm = Module["_ts_tree_cursor_reset_to_wasm"] = (a0, a1) => (_ts_tree_cursor_reset_to_wasm = Module["_ts_tree_cursor_reset_to_wasm"] = wasmExports["ts_tree_cursor_reset_to_wasm"])(a0, a1);
            var _ts_tree_cursor_goto_first_child_wasm = Module["_ts_tree_cursor_goto_first_child_wasm"] = (a0) => (_ts_tree_cursor_goto_first_child_wasm = Module["_ts_tree_cursor_goto_first_child_wasm"] = wasmExports["ts_tree_cursor_goto_first_child_wasm"])(a0);
            var _ts_tree_cursor_goto_last_child_wasm = Module["_ts_tree_cursor_goto_last_child_wasm"] = (a0) => (_ts_tree_cursor_goto_last_child_wasm = Module["_ts_tree_cursor_goto_last_child_wasm"] = wasmExports["ts_tree_cursor_goto_last_child_wasm"])(a0);
            var _ts_tree_cursor_goto_first_child_for_index_wasm = Module["_ts_tree_cursor_goto_first_child_for_index_wasm"] = (a0) => (_ts_tree_cursor_goto_first_child_for_index_wasm = Module["_ts_tree_cursor_goto_first_child_for_index_wasm"] = wasmExports["ts_tree_cursor_goto_first_child_for_index_wasm"])(a0);
            var _ts_tree_cursor_goto_first_child_for_position_wasm = Module["_ts_tree_cursor_goto_first_child_for_position_wasm"] = (a0) => (_ts_tree_cursor_goto_first_child_for_position_wasm = Module["_ts_tree_cursor_goto_first_child_for_position_wasm"] = wasmExports["ts_tree_cursor_goto_first_child_for_position_wasm"])(a0);
            var _ts_tree_cursor_goto_next_sibling_wasm = Module["_ts_tree_cursor_goto_next_sibling_wasm"] = (a0) => (_ts_tree_cursor_goto_next_sibling_wasm = Module["_ts_tree_cursor_goto_next_sibling_wasm"] = wasmExports["ts_tree_cursor_goto_next_sibling_wasm"])(a0);
            var _ts_tree_cursor_goto_previous_sibling_wasm = Module["_ts_tree_cursor_goto_previous_sibling_wasm"] = (a0) => (_ts_tree_cursor_goto_previous_sibling_wasm = Module["_ts_tree_cursor_goto_previous_sibling_wasm"] = wasmExports["ts_tree_cursor_goto_previous_sibling_wasm"])(a0);
            var _ts_tree_cursor_goto_descendant_wasm = Module["_ts_tree_cursor_goto_descendant_wasm"] = (a0, a1) => (_ts_tree_cursor_goto_descendant_wasm = Module["_ts_tree_cursor_goto_descendant_wasm"] = wasmExports["ts_tree_cursor_goto_descendant_wasm"])(a0, a1);
            var _ts_tree_cursor_goto_parent_wasm = Module["_ts_tree_cursor_goto_parent_wasm"] = (a0) => (_ts_tree_cursor_goto_parent_wasm = Module["_ts_tree_cursor_goto_parent_wasm"] = wasmExports["ts_tree_cursor_goto_parent_wasm"])(a0);
            var _ts_tree_cursor_current_node_type_id_wasm = Module["_ts_tree_cursor_current_node_type_id_wasm"] = (a0) => (_ts_tree_cursor_current_node_type_id_wasm = Module["_ts_tree_cursor_current_node_type_id_wasm"] = wasmExports["ts_tree_cursor_current_node_type_id_wasm"])(a0);
            var _ts_tree_cursor_current_node_state_id_wasm = Module["_ts_tree_cursor_current_node_state_id_wasm"] = (a0) => (_ts_tree_cursor_current_node_state_id_wasm = Module["_ts_tree_cursor_current_node_state_id_wasm"] = wasmExports["ts_tree_cursor_current_node_state_id_wasm"])(a0);
            var _ts_tree_cursor_current_node_is_named_wasm = Module["_ts_tree_cursor_current_node_is_named_wasm"] = (a0) => (_ts_tree_cursor_current_node_is_named_wasm = Module["_ts_tree_cursor_current_node_is_named_wasm"] = wasmExports["ts_tree_cursor_current_node_is_named_wasm"])(a0);
            var _ts_tree_cursor_current_node_is_missing_wasm = Module["_ts_tree_cursor_current_node_is_missing_wasm"] = (a0) => (_ts_tree_cursor_current_node_is_missing_wasm = Module["_ts_tree_cursor_current_node_is_missing_wasm"] = wasmExports["ts_tree_cursor_current_node_is_missing_wasm"])(a0);
            var _ts_tree_cursor_current_node_id_wasm = Module["_ts_tree_cursor_current_node_id_wasm"] = (a0) => (_ts_tree_cursor_current_node_id_wasm = Module["_ts_tree_cursor_current_node_id_wasm"] = wasmExports["ts_tree_cursor_current_node_id_wasm"])(a0);
            var _ts_tree_cursor_start_position_wasm = Module["_ts_tree_cursor_start_position_wasm"] = (a0) => (_ts_tree_cursor_start_position_wasm = Module["_ts_tree_cursor_start_position_wasm"] = wasmExports["ts_tree_cursor_start_position_wasm"])(a0);
            var _ts_tree_cursor_end_position_wasm = Module["_ts_tree_cursor_end_position_wasm"] = (a0) => (_ts_tree_cursor_end_position_wasm = Module["_ts_tree_cursor_end_position_wasm"] = wasmExports["ts_tree_cursor_end_position_wasm"])(a0);
            var _ts_tree_cursor_start_index_wasm = Module["_ts_tree_cursor_start_index_wasm"] = (a0) => (_ts_tree_cursor_start_index_wasm = Module["_ts_tree_cursor_start_index_wasm"] = wasmExports["ts_tree_cursor_start_index_wasm"])(a0);
            var _ts_tree_cursor_end_index_wasm = Module["_ts_tree_cursor_end_index_wasm"] = (a0) => (_ts_tree_cursor_end_index_wasm = Module["_ts_tree_cursor_end_index_wasm"] = wasmExports["ts_tree_cursor_end_index_wasm"])(a0);
            var _ts_tree_cursor_current_field_id_wasm = Module["_ts_tree_cursor_current_field_id_wasm"] = (a0) => (_ts_tree_cursor_current_field_id_wasm = Module["_ts_tree_cursor_current_field_id_wasm"] = wasmExports["ts_tree_cursor_current_field_id_wasm"])(a0);
            var _ts_tree_cursor_current_depth_wasm = Module["_ts_tree_cursor_current_depth_wasm"] = (a0) => (_ts_tree_cursor_current_depth_wasm = Module["_ts_tree_cursor_current_depth_wasm"] = wasmExports["ts_tree_cursor_current_depth_wasm"])(a0);
            var _ts_tree_cursor_current_descendant_index_wasm = Module["_ts_tree_cursor_current_descendant_index_wasm"] = (a0) => (_ts_tree_cursor_current_descendant_index_wasm = Module["_ts_tree_cursor_current_descendant_index_wasm"] = wasmExports["ts_tree_cursor_current_descendant_index_wasm"])(a0);
            var _ts_tree_cursor_current_node_wasm = Module["_ts_tree_cursor_current_node_wasm"] = (a0) => (_ts_tree_cursor_current_node_wasm = Module["_ts_tree_cursor_current_node_wasm"] = wasmExports["ts_tree_cursor_current_node_wasm"])(a0);
            var _ts_node_symbol_wasm = Module["_ts_node_symbol_wasm"] = (a0) => (_ts_node_symbol_wasm = Module["_ts_node_symbol_wasm"] = wasmExports["ts_node_symbol_wasm"])(a0);
            var _ts_node_field_name_for_child_wasm = Module["_ts_node_field_name_for_child_wasm"] = (a0, a1) => (_ts_node_field_name_for_child_wasm = Module["_ts_node_field_name_for_child_wasm"] = wasmExports["ts_node_field_name_for_child_wasm"])(a0, a1);
            var _ts_node_field_name_for_named_child_wasm = Module["_ts_node_field_name_for_named_child_wasm"] = (a0, a1) => (_ts_node_field_name_for_named_child_wasm = Module["_ts_node_field_name_for_named_child_wasm"] = wasmExports["ts_node_field_name_for_named_child_wasm"])(a0, a1);
            var _ts_node_children_by_field_id_wasm = Module["_ts_node_children_by_field_id_wasm"] = (a0, a1) => (_ts_node_children_by_field_id_wasm = Module["_ts_node_children_by_field_id_wasm"] = wasmExports["ts_node_children_by_field_id_wasm"])(a0, a1);
            var _ts_node_first_child_for_byte_wasm = Module["_ts_node_first_child_for_byte_wasm"] = (a0) => (_ts_node_first_child_for_byte_wasm = Module["_ts_node_first_child_for_byte_wasm"] = wasmExports["ts_node_first_child_for_byte_wasm"])(a0);
            var _ts_node_first_named_child_for_byte_wasm = Module["_ts_node_first_named_child_for_byte_wasm"] = (a0) => (_ts_node_first_named_child_for_byte_wasm = Module["_ts_node_first_named_child_for_byte_wasm"] = wasmExports["ts_node_first_named_child_for_byte_wasm"])(a0);
            var _ts_node_grammar_symbol_wasm = Module["_ts_node_grammar_symbol_wasm"] = (a0) => (_ts_node_grammar_symbol_wasm = Module["_ts_node_grammar_symbol_wasm"] = wasmExports["ts_node_grammar_symbol_wasm"])(a0);
            var _ts_node_child_count_wasm = Module["_ts_node_child_count_wasm"] = (a0) => (_ts_node_child_count_wasm = Module["_ts_node_child_count_wasm"] = wasmExports["ts_node_child_count_wasm"])(a0);
            var _ts_node_named_child_count_wasm = Module["_ts_node_named_child_count_wasm"] = (a0) => (_ts_node_named_child_count_wasm = Module["_ts_node_named_child_count_wasm"] = wasmExports["ts_node_named_child_count_wasm"])(a0);
            var _ts_node_child_wasm = Module["_ts_node_child_wasm"] = (a0, a1) => (_ts_node_child_wasm = Module["_ts_node_child_wasm"] = wasmExports["ts_node_child_wasm"])(a0, a1);
            var _ts_node_named_child_wasm = Module["_ts_node_named_child_wasm"] = (a0, a1) => (_ts_node_named_child_wasm = Module["_ts_node_named_child_wasm"] = wasmExports["ts_node_named_child_wasm"])(a0, a1);
            var _ts_node_child_by_field_id_wasm = Module["_ts_node_child_by_field_id_wasm"] = (a0, a1) => (_ts_node_child_by_field_id_wasm = Module["_ts_node_child_by_field_id_wasm"] = wasmExports["ts_node_child_by_field_id_wasm"])(a0, a1);
            var _ts_node_next_sibling_wasm = Module["_ts_node_next_sibling_wasm"] = (a0) => (_ts_node_next_sibling_wasm = Module["_ts_node_next_sibling_wasm"] = wasmExports["ts_node_next_sibling_wasm"])(a0);
            var _ts_node_prev_sibling_wasm = Module["_ts_node_prev_sibling_wasm"] = (a0) => (_ts_node_prev_sibling_wasm = Module["_ts_node_prev_sibling_wasm"] = wasmExports["ts_node_prev_sibling_wasm"])(a0);
            var _ts_node_next_named_sibling_wasm = Module["_ts_node_next_named_sibling_wasm"] = (a0) => (_ts_node_next_named_sibling_wasm = Module["_ts_node_next_named_sibling_wasm"] = wasmExports["ts_node_next_named_sibling_wasm"])(a0);
            var _ts_node_prev_named_sibling_wasm = Module["_ts_node_prev_named_sibling_wasm"] = (a0) => (_ts_node_prev_named_sibling_wasm = Module["_ts_node_prev_named_sibling_wasm"] = wasmExports["ts_node_prev_named_sibling_wasm"])(a0);
            var _ts_node_descendant_count_wasm = Module["_ts_node_descendant_count_wasm"] = (a0) => (_ts_node_descendant_count_wasm = Module["_ts_node_descendant_count_wasm"] = wasmExports["ts_node_descendant_count_wasm"])(a0);
            var _ts_node_parent_wasm = Module["_ts_node_parent_wasm"] = (a0) => (_ts_node_parent_wasm = Module["_ts_node_parent_wasm"] = wasmExports["ts_node_parent_wasm"])(a0);
            var _ts_node_child_with_descendant_wasm = Module["_ts_node_child_with_descendant_wasm"] = (a0) => (_ts_node_child_with_descendant_wasm = Module["_ts_node_child_with_descendant_wasm"] = wasmExports["ts_node_child_with_descendant_wasm"])(a0);
            var _ts_node_descendant_for_index_wasm = Module["_ts_node_descendant_for_index_wasm"] = (a0) => (_ts_node_descendant_for_index_wasm = Module["_ts_node_descendant_for_index_wasm"] = wasmExports["ts_node_descendant_for_index_wasm"])(a0);
            var _ts_node_named_descendant_for_index_wasm = Module["_ts_node_named_descendant_for_index_wasm"] = (a0) => (_ts_node_named_descendant_for_index_wasm = Module["_ts_node_named_descendant_for_index_wasm"] = wasmExports["ts_node_named_descendant_for_index_wasm"])(a0);
            var _ts_node_descendant_for_position_wasm = Module["_ts_node_descendant_for_position_wasm"] = (a0) => (_ts_node_descendant_for_position_wasm = Module["_ts_node_descendant_for_position_wasm"] = wasmExports["ts_node_descendant_for_position_wasm"])(a0);
            var _ts_node_named_descendant_for_position_wasm = Module["_ts_node_named_descendant_for_position_wasm"] = (a0) => (_ts_node_named_descendant_for_position_wasm = Module["_ts_node_named_descendant_for_position_wasm"] = wasmExports["ts_node_named_descendant_for_position_wasm"])(a0);
            var _ts_node_start_point_wasm = Module["_ts_node_start_point_wasm"] = (a0) => (_ts_node_start_point_wasm = Module["_ts_node_start_point_wasm"] = wasmExports["ts_node_start_point_wasm"])(a0);
            var _ts_node_end_point_wasm = Module["_ts_node_end_point_wasm"] = (a0) => (_ts_node_end_point_wasm = Module["_ts_node_end_point_wasm"] = wasmExports["ts_node_end_point_wasm"])(a0);
            var _ts_node_start_index_wasm = Module["_ts_node_start_index_wasm"] = (a0) => (_ts_node_start_index_wasm = Module["_ts_node_start_index_wasm"] = wasmExports["ts_node_start_index_wasm"])(a0);
            var _ts_node_end_index_wasm = Module["_ts_node_end_index_wasm"] = (a0) => (_ts_node_end_index_wasm = Module["_ts_node_end_index_wasm"] = wasmExports["ts_node_end_index_wasm"])(a0);
            var _ts_node_to_string_wasm = Module["_ts_node_to_string_wasm"] = (a0) => (_ts_node_to_string_wasm = Module["_ts_node_to_string_wasm"] = wasmExports["ts_node_to_string_wasm"])(a0);
            var _ts_node_children_wasm = Module["_ts_node_children_wasm"] = (a0) => (_ts_node_children_wasm = Module["_ts_node_children_wasm"] = wasmExports["ts_node_children_wasm"])(a0);
            var _ts_node_named_children_wasm = Module["_ts_node_named_children_wasm"] = (a0) => (_ts_node_named_children_wasm = Module["_ts_node_named_children_wasm"] = wasmExports["ts_node_named_children_wasm"])(a0);
            var _ts_node_descendants_of_type_wasm = Module["_ts_node_descendants_of_type_wasm"] = (a0, a1, a2, a3, a4, a5, a6) => (_ts_node_descendants_of_type_wasm = Module["_ts_node_descendants_of_type_wasm"] = wasmExports["ts_node_descendants_of_type_wasm"])(a0, a1, a2, a3, a4, a5, a6);
            var _ts_node_is_named_wasm = Module["_ts_node_is_named_wasm"] = (a0) => (_ts_node_is_named_wasm = Module["_ts_node_is_named_wasm"] = wasmExports["ts_node_is_named_wasm"])(a0);
            var _ts_node_has_changes_wasm = Module["_ts_node_has_changes_wasm"] = (a0) => (_ts_node_has_changes_wasm = Module["_ts_node_has_changes_wasm"] = wasmExports["ts_node_has_changes_wasm"])(a0);
            var _ts_node_has_error_wasm = Module["_ts_node_has_error_wasm"] = (a0) => (_ts_node_has_error_wasm = Module["_ts_node_has_error_wasm"] = wasmExports["ts_node_has_error_wasm"])(a0);
            var _ts_node_is_error_wasm = Module["_ts_node_is_error_wasm"] = (a0) => (_ts_node_is_error_wasm = Module["_ts_node_is_error_wasm"] = wasmExports["ts_node_is_error_wasm"])(a0);
            var _ts_node_is_missing_wasm = Module["_ts_node_is_missing_wasm"] = (a0) => (_ts_node_is_missing_wasm = Module["_ts_node_is_missing_wasm"] = wasmExports["ts_node_is_missing_wasm"])(a0);
            var _ts_node_is_extra_wasm = Module["_ts_node_is_extra_wasm"] = (a0) => (_ts_node_is_extra_wasm = Module["_ts_node_is_extra_wasm"] = wasmExports["ts_node_is_extra_wasm"])(a0);
            var _ts_node_parse_state_wasm = Module["_ts_node_parse_state_wasm"] = (a0) => (_ts_node_parse_state_wasm = Module["_ts_node_parse_state_wasm"] = wasmExports["ts_node_parse_state_wasm"])(a0);
            var _ts_node_next_parse_state_wasm = Module["_ts_node_next_parse_state_wasm"] = (a0) => (_ts_node_next_parse_state_wasm = Module["_ts_node_next_parse_state_wasm"] = wasmExports["ts_node_next_parse_state_wasm"])(a0);
            var _ts_query_matches_wasm = Module["_ts_query_matches_wasm"] = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) => (_ts_query_matches_wasm = Module["_ts_query_matches_wasm"] = wasmExports["ts_query_matches_wasm"])(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
            var _ts_query_captures_wasm = Module["_ts_query_captures_wasm"] = (a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10) => (_ts_query_captures_wasm = Module["_ts_query_captures_wasm"] = wasmExports["ts_query_captures_wasm"])(a0, a1, a2, a3, a4, a5, a6, a7, a8, a9, a10);
            var _iswalpha = Module["_iswalpha"] = (a0) => (_iswalpha = Module["_iswalpha"] = wasmExports["iswalpha"])(a0);
            var _iswblank = Module["_iswblank"] = (a0) => (_iswblank = Module["_iswblank"] = wasmExports["iswblank"])(a0);
            var _iswdigit = Module["_iswdigit"] = (a0) => (_iswdigit = Module["_iswdigit"] = wasmExports["iswdigit"])(a0);
            var _iswlower = Module["_iswlower"] = (a0) => (_iswlower = Module["_iswlower"] = wasmExports["iswlower"])(a0);
            var _iswupper = Module["_iswupper"] = (a0) => (_iswupper = Module["_iswupper"] = wasmExports["iswupper"])(a0);
            var _iswxdigit = Module["_iswxdigit"] = (a0) => (_iswxdigit = Module["_iswxdigit"] = wasmExports["iswxdigit"])(a0);
            var _memchr = Module["_memchr"] = (a0, a1, a2) => (_memchr = Module["_memchr"] = wasmExports["memchr"])(a0, a1, a2);
            var _strlen = Module["_strlen"] = (a0) => (_strlen = Module["_strlen"] = wasmExports["strlen"])(a0);
            var _strcmp = Module["_strcmp"] = (a0, a1) => (_strcmp = Module["_strcmp"] = wasmExports["strcmp"])(a0, a1);
            var _strncat = Module["_strncat"] = (a0, a1, a2) => (_strncat = Module["_strncat"] = wasmExports["strncat"])(a0, a1, a2);
            var _strncpy = Module["_strncpy"] = (a0, a1, a2) => (_strncpy = Module["_strncpy"] = wasmExports["strncpy"])(a0, a1, a2);
            var _towlower = Module["_towlower"] = (a0) => (_towlower = Module["_towlower"] = wasmExports["towlower"])(a0);
            var _towupper = Module["_towupper"] = (a0) => (_towupper = Module["_towupper"] = wasmExports["towupper"])(a0);
            var _setThrew = /* @__PURE__ */ __name((a0, a1) => (_setThrew = wasmExports["setThrew"])(a0, a1), "_setThrew");
            var __emscripten_stack_restore = /* @__PURE__ */ __name((a0) => (__emscripten_stack_restore = wasmExports["_emscripten_stack_restore"])(a0), "__emscripten_stack_restore");
            var __emscripten_stack_alloc = /* @__PURE__ */ __name((a0) => (__emscripten_stack_alloc = wasmExports["_emscripten_stack_alloc"])(a0), "__emscripten_stack_alloc");
            var _emscripten_stack_get_current = /* @__PURE__ */ __name(() => (_emscripten_stack_get_current = wasmExports["emscripten_stack_get_current"])(), "_emscripten_stack_get_current");
            var dynCall_jiji = Module["dynCall_jiji"] = (a0, a1, a2, a3, a4) => (dynCall_jiji = Module["dynCall_jiji"] = wasmExports["dynCall_jiji"])(a0, a1, a2, a3, a4);
            var _orig$ts_parser_timeout_micros = Module["_orig$ts_parser_timeout_micros"] = (a0) => (_orig$ts_parser_timeout_micros = Module["_orig$ts_parser_timeout_micros"] = wasmExports["orig$ts_parser_timeout_micros"])(a0);
            var _orig$ts_parser_set_timeout_micros = Module["_orig$ts_parser_set_timeout_micros"] = (a0, a1) => (_orig$ts_parser_set_timeout_micros = Module["_orig$ts_parser_set_timeout_micros"] = wasmExports["orig$ts_parser_set_timeout_micros"])(a0, a1);
            Module["setValue"] = setValue;
            Module["getValue"] = getValue;
            Module["UTF8ToString"] = UTF8ToString;
            Module["stringToUTF8"] = stringToUTF8;
            Module["lengthBytesUTF8"] = lengthBytesUTF8;
            Module["AsciiToString"] = AsciiToString;
            Module["stringToUTF16"] = stringToUTF16;
            Module["loadWebAssemblyModule"] = loadWebAssemblyModule;
            var calledRun;
            dependenciesFulfilled = /* @__PURE__ */ __name(function runCaller() {
                if (!calledRun)
                    run();
                if (!calledRun)
                    dependenciesFulfilled = runCaller;
            }, "runCaller");
            function callMain(args2 = []) {
                var entryFunction = resolveGlobalSymbol("main").sym;
                if (!entryFunction)
                    return;
                args2.unshift(thisProgram);
                var argc = args2.length;
                var argv = stackAlloc((argc + 1) * 4);
                var argv_ptr = argv;
                args2.forEach((arg) => {
                    LE_HEAP_STORE_U32((argv_ptr >> 2) * 4, stringToUTF8OnStack(arg));
                    argv_ptr += 4;
                });
                LE_HEAP_STORE_U32((argv_ptr >> 2) * 4, 0);
                try {
                    var ret = entryFunction(argc, argv);
                    exitJS(ret, 
                    /* implicit = */
                    true);
                    return ret;
                }
                catch (e) {
                    return handleException(e);
                }
            }
            __name(callMain, "callMain");
            function run(args2 = arguments_) {
                if (runDependencies > 0) {
                    return;
                }
                preRun();
                if (runDependencies > 0) {
                    return;
                }
                function doRun() {
                    if (calledRun)
                        return;
                    calledRun = true;
                    Module["calledRun"] = true;
                    if (ABORT)
                        return;
                    initRuntime();
                    preMain();
                    readyPromiseResolve(Module);
                    Module["onRuntimeInitialized"]?.();
                    if (shouldRunNow)
                        callMain(args2);
                    postRun();
                }
                __name(doRun, "doRun");
                if (Module["setStatus"]) {
                    Module["setStatus"]("Running...");
                    setTimeout(function () {
                        setTimeout(function () {
                            Module["setStatus"]("");
                        }, 1);
                        doRun();
                    }, 1);
                }
                else {
                    doRun();
                }
            }
            __name(run, "run");
            if (Module["preInit"]) {
                if (typeof Module["preInit"] == "function")
                    Module["preInit"] = [Module["preInit"]];
                while (Module["preInit"].length > 0) {
                    Module["preInit"].pop()();
                }
            }
            var shouldRunNow = true;
            if (Module["noInitialRun"])
                shouldRunNow = false;
            run();
            moduleRtn = readyPromise;
            return moduleRtn;
        };
    })();
    var tree_sitter_default = Module2;
    // src/bindings.ts
    var Module3 = null;
    async function initializeBinding(moduleOptions) {
        if (!Module3) {
            Module3 = await tree_sitter_default(moduleOptions);
        }
        return Module3;
    }
    __name(initializeBinding, "initializeBinding");
    function checkModule() {
        return !!Module3;
    }
    __name(checkModule, "checkModule");
    // src/parser.ts
    var TRANSFER_BUFFER;
    var LANGUAGE_VERSION;
    var MIN_COMPATIBLE_VERSION;
    var Parser = class {
        static {
            __name(this, "Parser");
        }
        /**
         * This must always be called before creating a Parser.
         *
         * You can optionally pass in options to configure the WASM module, the most common
         * one being `locateFile` to help the module find the `.wasm` file.
         */
        static async init(moduleOptions) {
            setModule(await initializeBinding(moduleOptions));
            TRANSFER_BUFFER = C._ts_init();
            LANGUAGE_VERSION = C.getValue(TRANSFER_BUFFER, "i32");
            MIN_COMPATIBLE_VERSION = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
        }
        /**
         * Create a new parser.
         */
        constructor() {
            /** @internal */
            this[0] = 0;
            // Internal handle for WASM
            /** @internal */
            this[1] = 0;
            // Internal handle for WASM
            /** @internal */
            this.logCallback = null;
            /** The parser's current language. */
            this.language = null;
            this.initialize();
        }
        /** @internal */
        initialize() {
            if (!checkModule()) {
                throw new Error("cannot construct a Parser before calling `init()`");
            }
            C._ts_parser_new_wasm();
            this[0] = C.getValue(TRANSFER_BUFFER, "i32");
            this[1] = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
        }
        /** Delete the parser, freeing its resources. */
        delete() {
            C._ts_parser_delete(this[0]);
            C._free(this[1]);
            this[0] = 0;
            this[1] = 0;
        }
        /**
         * Set the language that the parser should use for parsing.
         *
         * If the language was not successfully assigned, an error will be thrown.
         * This happens if the language was generated with an incompatible
         * version of the Tree-sitter CLI. Check the language's version using
         * {@link Language#version} and compare it to this library's
         * {@link LANGUAGE_VERSION} and {@link MIN_COMPATIBLE_VERSION} constants.
         */
        setLanguage(language) {
            let address;
            if (!language) {
                address = 0;
                this.language = null;
            }
            else if (language.constructor === Language) {
                address = language[0];
                const version = C._ts_language_version(address);
                if (version < MIN_COMPATIBLE_VERSION || LANGUAGE_VERSION < version) {
                    throw new Error(`Incompatible language version ${version}. Compatibility range ${MIN_COMPATIBLE_VERSION} through ${LANGUAGE_VERSION}.`);
                }
                this.language = language;
            }
            else {
                throw new Error("Argument must be a Language");
            }
            C._ts_parser_set_language(this[0], address);
            return this;
        }
        /**
         * Parse a slice of UTF8 text.
         *
         * @param {string | ParseCallback} callback - The UTF8-encoded text to parse or a callback function.
         *
         * @param {Tree | null} [oldTree] - A previous syntax tree parsed from the same document. If the text of the
         *   document has changed since `oldTree` was created, then you must edit `oldTree` to match
         *   the new text using {@link Tree#edit}.
         *
         * @param {ParseOptions} [options] - Options for parsing the text.
         *  This can be used to set the included ranges, or a progress callback.
         *
         * @returns {Tree | null} A {@link Tree} if parsing succeeded, or `null` if:
         *  - The parser has not yet had a language assigned with {@link Parser#setLanguage}.
         *  - The progress callback returned true.
         */
        parse(callback, oldTree, options) {
            if (typeof callback === "string") {
                C.currentParseCallback = (index) => callback.slice(index);
            }
            else if (typeof callback === "function") {
                C.currentParseCallback = callback;
            }
            else {
                throw new Error("Argument must be a string or a function");
            }
            if (options?.progressCallback) {
                C.currentProgressCallback = options.progressCallback;
            }
            else {
                C.currentProgressCallback = null;
            }
            if (this.logCallback) {
                C.currentLogCallback = this.logCallback;
                C._ts_parser_enable_logger_wasm(this[0], 1);
            }
            else {
                C.currentLogCallback = null;
                C._ts_parser_enable_logger_wasm(this[0], 0);
            }
            let rangeCount = 0;
            let rangeAddress = 0;
            if (options?.includedRanges) {
                rangeCount = options.includedRanges.length;
                rangeAddress = C._calloc(rangeCount, SIZE_OF_RANGE);
                let address = rangeAddress;
                for (let i2 = 0; i2 < rangeCount; i2++) {
                    marshalRange(address, options.includedRanges[i2]);
                    address += SIZE_OF_RANGE;
                }
            }
            const treeAddress = C._ts_parser_parse_wasm(this[0], this[1], oldTree ? oldTree[0] : 0, rangeAddress, rangeCount);
            if (!treeAddress) {
                C.currentParseCallback = null;
                C.currentLogCallback = null;
                C.currentProgressCallback = null;
                return null;
            }
            if (!this.language) {
                throw new Error("Parser must have a language to parse");
            }
            const result = new Tree(INTERNAL, treeAddress, this.language, C.currentParseCallback);
            C.currentParseCallback = null;
            C.currentLogCallback = null;
            C.currentProgressCallback = null;
            return result;
        }
        /**
         * Instruct the parser to start the next parse from the beginning.
         *
         * If the parser previously failed because of a timeout, cancellation,
         * or callback, then by default, it will resume where it left off on the
         * next call to {@link Parser#parse} or other parsing functions.
         * If you don't want to resume, and instead intend to use this parser to
         * parse some other document, you must call `reset` first.
         */
        reset() {
            C._ts_parser_reset(this[0]);
        }
        /** Get the ranges of text that the parser will include when parsing. */
        getIncludedRanges() {
            C._ts_parser_included_ranges_wasm(this[0]);
            const count = C.getValue(TRANSFER_BUFFER, "i32");
            const buffer = C.getValue(TRANSFER_BUFFER + SIZE_OF_INT, "i32");
            const result = new Array(count);
            if (count > 0) {
                let address = buffer;
                for (let i2 = 0; i2 < count; i2++) {
                    result[i2] = unmarshalRange(address);
                    address += SIZE_OF_RANGE;
                }
                C._free(buffer);
            }
            return result;
        }
        /**
         * @deprecated since version 0.25.0, prefer passing a progress callback to {@link Parser#parse}
         *
         * Get the duration in microseconds that parsing is allowed to take.
         *
         * This is set via {@link Parser#setTimeoutMicros}.
         */
        getTimeoutMicros() {
            return C._ts_parser_timeout_micros(this[0]);
        }
        /**
         * @deprecated since version 0.25.0, prefer passing a progress callback to {@link Parser#parse}
         *
         * Set the maximum duration in microseconds that parsing should be allowed
         * to take before halting.
         *
         * If parsing takes longer than this, it will halt early, returning `null`.
         * See {@link Parser#parse} for more information.
         */
        setTimeoutMicros(timeout) {
            C._ts_parser_set_timeout_micros(this[0], 0, timeout);
        }
        /** Set the logging callback that a parser should use during parsing. */
        setLogger(callback) {
            if (!callback) {
                this.logCallback = null;
            }
            else if (typeof callback !== "function") {
                throw new Error("Logger callback must be a function");
            }
            else {
                this.logCallback = callback;
            }
            return this;
        }
        /** Get the parser's current logger. */
        getLogger() {
            return this.logCallback;
        }
    };
    return {
        CaptureQuantifier,
        LANGUAGE_VERSION,
        Language,
        LookaheadIterator,
        MIN_COMPATIBLE_VERSION,
        Node,
        Parser,
        Query,
        Tree,
        TreeCursor
    };
}));
//# sourceMappingURL=data:application/json;base64,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