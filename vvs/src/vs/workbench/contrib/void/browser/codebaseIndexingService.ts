/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ICodebaseIndexingService as ICodebaseIndexingServiceInterface, IIndexingProgress, IIndexingOptions, ISearchOptions, ISearchResult, IIndexStats, ICodeChunk, IEmbeddingCache, IVectorSearchEngine, ITreeSitterService as ITreeSitterServiceInterface, IRecentFileActivity } from '../common/codebaseIndexingTypes.js';
import { ITreeSitterService } from './treeSitterService.js';
import { IVoidWebSocketService } from './voidWebSocketService.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { IWorkspaceContextService } from '../../../../platform/workspace/common/workspace.js';
import { IProgressService, ProgressLocation, IProgress, IProgressStep } from '../../../../platform/progress/common/progress.js';
import { INotificationService } from '../../../../platform/notification/common/notification.js';
import { IStorageService, StorageScope, StorageTarget } from '../../../../platform/storage/common/storage.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { Disposable, IDisposable } from '../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { URI } from '../../../../base/common/uri.js';
import { CancellationToken, CancellationTokenSource } from '../../../../base/common/cancellation.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { MessageType } from './voidWebSocketTypes.js';
import { FileChangeType } from '../../../../platform/files/common/files.js';
import { RunOnceScheduler } from '../../../../base/common/async.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';

export const ICodebaseIndexingService = createDecorator<ICodebaseIndexingServiceInterface>('codebaseIndexingService');

// Constants
const STORAGE_KEY_INDEXED_FILES = 'codebaseIndexing.indexedFiles';
const STORAGE_KEY_INDEX_STATS = 'codebaseIndexing.stats';
const STORAGE_KEY_EMBEDDING_CACHE = 'codebaseIndexing.embeddingCache';

const DEFAULT_EXCLUDE_PATTERNS = [
	'node_modules',
	'.git',
	'dist',
	'build',
	'out',
	'.next',
	'.vscode',
	'coverage',
	'.nyc_output',
	'*.min.js',
	'*.map'
];

const DEFAULT_MAX_FILE_SIZE = 1024 * 1024; // 1MB
const DEFAULT_BATCH_SIZE = 10;

const SUPPORTED_EXTENSIONS = [
	'ts', 'tsx', 'js', 'jsx', 'py', 'java', 'c', 'cpp', 'cs', 'go', 'rs', 'php',
	'rb', 'swift', 'kt', 'scala', 'r', 'dart', 'lua', 'perl', 'rust', 'sh',
	'sql', 'html', 'css', 'scss', 'less', 'json', 'xml', 'yaml', 'yml', 'md',
	'vue', 'svelte', 'astro'
];

class EmbeddingCache implements IEmbeddingCache {
	private cache = new Map<string, number[]>();

	constructor(
		private readonly storageService: IStorageService
	) {
		// Load cache from storage
		const stored = this.storageService.get(STORAGE_KEY_EMBEDDING_CACHE, StorageScope.WORKSPACE);
		if (stored) {
			try {
				const parsed = JSON.parse(stored);
				Object.entries(parsed).forEach(([key, value]) => {
					this.cache.set(key, value as number[]);
				});
			} catch (e) {
				// Ignore parse errors
			}
		}
	}

	async get(chunkId: string): Promise<number[] | null> {
		return this.cache.get(chunkId) || null;
	}

	async set(chunkId: string, embedding: number[]): Promise<void> {
		this.cache.set(chunkId, embedding);
		this.persist();
	}

	async has(chunkId: string): Promise<boolean> {
		return this.cache.has(chunkId);
	}

	async delete(chunkId: string): Promise<void> {
		this.cache.delete(chunkId);
		this.persist();
	}

	async clear(): Promise<void> {
		this.cache.clear();
		this.persist();
	}

	private persist(): void {
		const obj: Record<string, number[]> = {};
		this.cache.forEach((value, key) => {
			obj[key] = value;
		});
		this.storageService.store(STORAGE_KEY_EMBEDDING_CACHE, JSON.stringify(obj), StorageScope.WORKSPACE, StorageTarget.MACHINE);
	}
}

class VectorSearchEngine implements IVectorSearchEngine {
	private vectors = new Map<string, { vector: number[]; metadata?: any }>();

	addVector(id: string, vector: number[], metadata?: any): void {
		this.vectors.set(id, { vector, metadata });
	}

	removeVector(id: string): void {
		this.vectors.delete(id);
	}

	search(queryVector: number[], k: number): Array<{ id: string; score: number; metadata?: any }> {
		const results: Array<{ id: string; score: number; metadata?: any }> = [];

		this.vectors.forEach((data, id) => {
			const score = this.cosineSimilarity(queryVector, data.vector);
			results.push({ id, score, metadata: data.metadata });
		});

		// Sort by score descending and take top k
		results.sort((a, b) => b.score - a.score);
		return results.slice(0, k);
	}

	clear(): void {
		this.vectors.clear();
	}

	size(): number {
		return this.vectors.size;
	}

	private cosineSimilarity(a: number[], b: number[]): number {
		if (a.length !== b.length) {
			throw new Error('Vectors must have the same length');
		}

		let dotProduct = 0;
		let normA = 0;
		let normB = 0;

		for (let i = 0; i < a.length; i++) {
			dotProduct += a[i] * b[i];
			normA += a[i] * a[i];
			normB += b[i] * b[i];
		}

		normA = Math.sqrt(normA);
		normB = Math.sqrt(normB);

		if (normA === 0 || normB === 0) {
			return 0;
		}

		return dotProduct / (normA * normB);
	}
}

export class CodebaseIndexingService extends Disposable implements ICodebaseIndexingServiceInterface {
	readonly _serviceBrand: undefined;

	private readonly _onDidChangeProgress = this._register(new Emitter<IIndexingProgress>());
	readonly onDidChangeProgress: Event<IIndexingProgress> = this._onDidChangeProgress.event;

	private readonly _onDidChangeIndexingStatus = this._register(new Emitter<boolean>());
	readonly onDidChangeIndexingStatus: Event<boolean> = this._onDidChangeIndexingStatus.event;

	private _isIndexing = false;

	// New state management for improved progress tracking
	private _totalFilesToProcess: number = 0;
	private _filesRequiringProcessingCount: number = 0; // Files that need parsing/embedding in this session
	private _filesParsedCount: number = 0;
	private _filesEmbeddedSuccessfullyCount: number = 0;
	private _filesWithEmbeddingsErrorsCount: number = 0;
	private _completedChunks: number = 0;
	private _totalChunks: number = 0;
	private _recentlyProcessedFilesMap = new Map<string, IRecentFileActivity>();
	private _currentOverallStatusText: string = 'Idle';

	// Track real vector counts from server
	private _realVectorCount = 0;
	private _expectedVectorCount = 0; // Total chunks expected to be indexed from the current operation

	private readonly indexedFiles = new Set<string>();
	private readonly parsedFiles = new Set<string>(); // Track files that have been parsed but not necessarily embedded
	private readonly embeddedFiles = new Set<string>(); // Track files that have been successfully embedded
	private readonly fileChunks = new Map<string, ICodeChunk[]>();
	private readonly embeddingCache: IEmbeddingCache;
	private readonly searchEngine: IVectorSearchEngine;
	private cancellationTokenSource?: CancellationTokenSource;

	// File watching for incremental indexing
	private fileWatchers = new Map<string, IDisposable>();
	private pendingFileUpdates = new Set<string>();
	private updateDebouncer: any;

	constructor(
		@ITreeSitterService private readonly treeSitterService: ITreeSitterServiceInterface,
		@IVoidWebSocketService private readonly webSocketService: IVoidWebSocketService,
		@ILogService private readonly logService: ILogService,
		@IFileService private readonly fileService: IFileService,
		@IWorkspaceContextService private readonly workspaceService: IWorkspaceContextService,
		@IProgressService private readonly progressService: IProgressService,
		@INotificationService private readonly notificationService: INotificationService,
		@IStorageService private readonly storageService: IStorageService
	) {
		super();

		this.embeddingCache = new EmbeddingCache(storageService);
		this.searchEngine = new VectorSearchEngine();

		// Load indexed files from storage
		const storedFiles = this.storageService.get(STORAGE_KEY_INDEXED_FILES, StorageScope.WORKSPACE);
		if (storedFiles) {
			try {
				const files = JSON.parse(storedFiles);
				files.forEach((file: string) => this.indexedFiles.add(file));
			} catch (e) {
				// Ignore parse errors
			}
		}

		// Load embedded files from storage (files that have been successfully embedded)
		const storedEmbeddedFiles = this.storageService.get('codebaseIndexing.embeddedFiles', StorageScope.WORKSPACE);
		if (storedEmbeddedFiles) {
			try {
				const files = JSON.parse(storedEmbeddedFiles);
				files.forEach((file: string) => this.embeddedFiles.add(file));
			} catch (e) {
				// Ignore parse errors
			}
		}

		// Initialize file watching
		this.initializeFileWatching();

		this.logService.info('[CodebaseIndexingService] Initialized');
	}

	private initializeFileWatching(): void {
		// Watch workspace folders for changes
		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		for (const folder of workspaceFolders) {
			this.watchFolder(folder.uri);
		}

		// Listen for workspace folder changes
		this._register(this.workspaceService.onDidChangeWorkspaceFolders(e => {
			// Stop watching removed folders
			for (const removed of e.removed) {
				const watcher = this.fileWatchers.get(removed.uri.toString());
				if (watcher) {
					watcher.dispose();
					this.fileWatchers.delete(removed.uri.toString());
				}
			}

			// Start watching added folders
			for (const added of e.added) {
				this.watchFolder(added.uri);
			}
		}));

		// Set up debouncer for file updates
		this.updateDebouncer = this._register(new RunOnceScheduler(() => {
			this.processPendingFileUpdates();
		}, 1000)); // 1 second debounce
	}

	private watchFolder(folderUri: URI): void {
		const watcher = this.fileService.watch(folderUri, {
			recursive: true,
			excludes: DEFAULT_EXCLUDE_PATTERNS
		});

		this.fileWatchers.set(folderUri.toString(), watcher);

		// Listen for file changes
		this._register(this.fileService.onDidFilesChange(e => {
			// Check each affected resource
			if (e.affects(folderUri, FileChangeType.ADDED, FileChangeType.UPDATED, FileChangeType.DELETED)) {
				// Process added files
				for (const added of e.rawAdded) {
					if (this.shouldIndexFile(added)) {
						this.pendingFileUpdates.add(added.toString());
						this.updateDebouncer.schedule();
					}
				}

				// Process updated files
				for (const updated of e.rawUpdated) {
					if (this.indexedFiles.has(updated.toString()) && this.shouldIndexFile(updated)) {
						this.pendingFileUpdates.add(updated.toString());
						this.updateDebouncer.schedule();
					}
				}

				// Process deleted files
				for (const deleted of e.rawDeleted) {
					if (this.indexedFiles.has(deleted.toString())) {
						this.removeFile(deleted);
					}
				}
			}
		}));
	}

	private async processPendingFileUpdates(): Promise<void> {
		if (this.pendingFileUpdates.size === 0 || this._isIndexing) {
			return;
		}

		const filesToUpdate = Array.from(this.pendingFileUpdates);
		this.pendingFileUpdates.clear();

		this.logService.info(`[CodebaseIndexingService] Processing ${filesToUpdate.length} file updates`);

		for (const fileUri of filesToUpdate) {
			try {
				await this.indexFile(URI.parse(fileUri));
			} catch (error) {
				this.logService.error(`[CodebaseIndexingService] Error updating file ${fileUri}:`, error);
			}
		}
	}

	private shouldIndexFile(uri: URI): boolean {
		const path = uri.fsPath;

		// Check file extension
		const ext = path.split('.').pop()?.toLowerCase();
		if (!ext || !SUPPORTED_EXTENSIONS.includes(ext)) {
			return false;
		}

		// Check exclude patterns
		for (const pattern of DEFAULT_EXCLUDE_PATTERNS) {
			if (path.includes(pattern)) {
				return false;
			}
		}

		return true;
	}

	async startIndexing(paths?: URI[], options?: IIndexingOptions): Promise<void> {
		await this.clearIndex(); // Automatically clear the index before starting

		if (this._isIndexing) {
			this.notificationService.warn('Indexing is already in progress');
			return;
		}

		this._isIndexing = true;
		this._onDidChangeIndexingStatus.fire(true);
		this.cancellationTokenSource = new CancellationTokenSource();

		// Reset state for new indexing session
		this._totalFilesToProcess = 0;
		this._filesRequiringProcessingCount = 0;
		this._filesParsedCount = 0;
		this._filesEmbeddedSuccessfullyCount = 0;
		this._filesWithEmbeddingsErrorsCount = 0;
		this._completedChunks = 0;
		this._totalChunks = 0;
		this._recentlyProcessedFilesMap.clear();
		this._currentOverallStatusText = 'Collecting files...';

		const workspaceFolders = this.workspaceService.getWorkspace().folders;
		if (workspaceFolders.length === 0) {
			this.notificationService.error('No workspace folder open');
			this.stopIndexing();
			return;
		}

		const targetPaths = paths || workspaceFolders.map(f => f.uri);
		const excludePatterns = options?.excludePatterns || DEFAULT_EXCLUDE_PATTERNS;
		const maxFileSize = options?.maxFileSizeBytes || DEFAULT_MAX_FILE_SIZE;

		this.logService.info(`[CodebaseIndexingService] Starting indexing for ${targetPaths.length} paths`);

		await this.progressService.withProgress({
			location: ProgressLocation.Notification,
			title: 'Indexing codebase',
			cancellable: true
		}, async (progress: IProgress<IProgressStep>, progressCancellationToken?: CancellationToken) => {
			let cancellationListener: IDisposable = Disposable.None;
			if (progressCancellationToken) {
				cancellationListener = progressCancellationToken.onCancellationRequested(() => {
					this.logService.info('[CodebaseIndexingService] Indexing cancellation requested via progress UI.');
					this.cancellationTokenSource?.cancel();
				});
			}

			try {
				// Phase 1: Collect files
				this._emitProgressUpdate();
				progress.report({ increment: 0, message: 'Collecting files...' });

				const files = await this.collectFiles(targetPaths, excludePatterns, maxFileSize);
				this._totalFilesToProcess = files.length;
				this.logService.info(`[CodebaseIndexingService] Found ${files.length} files to index`);

				if (files.length === 0) {
					this._currentOverallStatusText = 'No files found to index';
					this._emitProgressUpdate();
					this.notificationService.info('No files found to index');
					this.stopIndexing();
					return;
				}

				// Determine files that actually need processing in this session
				const filesToProcessForEmbedding = files.filter(file => !this.embeddedFiles.has(file.fsPath));
				this._filesRequiringProcessingCount = filesToProcessForEmbedding.length;

				this.logService.info(`[CodebaseIndexingService] ${this._filesRequiringProcessingCount} files require embedding in this session (${files.length - this._filesRequiringProcessingCount} were already marked as embedded).`);

				// Phase 2: Parse files (only files that haven't been embedded yet)
				const allChunks: Array<{ uri: URI; chunks: ICodeChunk[] }> = [];
				this.logService.info(`[CodebaseIndexingService] Starting parsing loop for ${filesToProcessForEmbedding.length} files.`);

				for (const file of filesToProcessForEmbedding) {
					if (this.cancellationTokenSource?.token.isCancellationRequested) {
						this.logService.info(`[CodebaseIndexingService] Parsing loop cancelled.`);
						break;
					}

					const fileName = file.path.split('/').pop() || file.fsPath;
					this.logService.info(`[CodebaseIndexingService] LOOP Iteration: Processing file ${file.fsPath} (${this._filesParsedCount + 1} of ${this._filesRequiringProcessingCount})`);

					try {
						// Update status for this file
						this._currentOverallStatusText = `Parsing ${this._filesParsedCount + 1} of ${this._filesRequiringProcessingCount} files: ${fileName}`;
						this._recentlyProcessedFilesMap.set(file.fsPath, {
							filePath: file.fsPath,
							fileName,
							status: 'parsing',
							timestamp: new Date()
						});
						this._emitProgressUpdate();

						const chunks = await this.parseFile(file);
						this.logService.info(`[CodebaseIndexingService] -> parseFile for ${file.fsPath} returned ${chunks.length} chunks.`);

						if (chunks.length > 0) {
							allChunks.push({ uri: file, chunks });
							this.fileChunks.set(file.toString(), chunks);
							this.parsedFiles.add(file.fsPath);
							this.logService.info(`[CodebaseIndexingService] --> Added ${chunks.length} chunks from ${file.fsPath}. Total accumulated chunks in allChunks: ${allChunks.reduce((sum, item) => sum + item.chunks.length, 0)}`);
						}

						this._filesParsedCount++;

						// Update file status to parsed
						this._recentlyProcessedFilesMap.set(file.fsPath, {
							filePath: file.fsPath,
							fileName,
							status: 'parsed',
							timestamp: new Date()
						});

					} catch (error) {
						this.logService.error(`[CodebaseIndexingService] Error parsing file ${file.fsPath}:`, error);
						// DO NOT increment _filesWithEmbeddingsErrorsCount here, this is a parsing error.
						// Update file status to error
						this._recentlyProcessedFilesMap.set(file.fsPath, {
							filePath: file.fsPath,
							fileName,
							status: 'error',
							timestamp: new Date(),
							errorDetails: error instanceof Error ? error.message : String(error)
						});
					}

					const parseProgress = (this._filesParsedCount / this._filesRequiringProcessingCount) * 100; // Parsing makes up 100% of this phase
					progress.report({ increment: parseProgress / this._filesRequiringProcessingCount, message: `Parsing files... (${this._filesParsedCount}/${this._filesRequiringProcessingCount})` });
				}

				this._totalChunks = allChunks.reduce((sum, item) => sum + item.chunks.length, 0);
				this._expectedVectorCount = this._totalChunks; // Track expected vectors for the current operation
				this.logService.info(`[CodebaseIndexingService] Parsed ${this._filesParsedCount} files for this session, found ${this._totalChunks} chunks to embed`);

				// Phase 3: Generate embeddings
				this._currentOverallStatusText = 'Generating embeddings...';
				this._emitProgressUpdate();
				progress.report({ message: 'Generating embeddings...' });

				const batchSize = DEFAULT_BATCH_SIZE; // Ensure batchSize is defined here
				const totalChunksToEmbed = this._totalChunks;

				for (let i = 0; i < allChunks.length; i += batchSize) {
					if (this.cancellationTokenSource?.token.isCancellationRequested) {
						break;
					}

					const batch = allChunks.slice(i, i + batchSize);
					const chunksInBatch = batch.flatMap(item => item.chunks);

					try {
						await this.generateEmbeddings(chunksInBatch); // This promise now resolves after server confirms embeddings

						// Mark files in this batch as indexed in the indexedFiles set
						batch.forEach(item => {
							this.indexedFiles.add(item.uri.fsPath);
							this.embeddedFiles.add(item.uri.fsPath); // Track as successfully embedded
						});

					} catch (error) {
						this.logService.error(`[CodebaseIndexingService] Error generating embeddings for a batch:`, error);
						// Errors are handled by server progress messages for individual files in the batch
					}

					// UI progress update based on overall chunks processed (from server)
					progress.report({ message: `Generating embeddings... (${this._completedChunks}/${totalChunksToEmbed})` });
				}

				// Final update to ensure recentFiles status reflects completion for successfully processed files
				this._recentlyProcessedFilesMap.forEach((rf, filePath) => {
					if (this.embeddedFiles.has(filePath) && rf.status !== 'error') {
						rf.status = 'indexed';
					}
				});
				this._emitProgressUpdate(); // Trigger a final event with updated recentFiles

				// Final stats update
				const finalEmbeddedFilesInSession = this._filesEmbeddedSuccessfullyCount;

				// Save indexed files
				this.storageService.store(
					STORAGE_KEY_INDEXED_FILES,
					JSON.stringify(Array.from(this.indexedFiles)),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);

				// Save embedded files (successfully embedded)
				this.storageService.store(
					'codebaseIndexing.embeddedFiles',
					JSON.stringify(Array.from(this.embeddedFiles)),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);

				// Save stats
				const statsToStore: IIndexStats = {
					totalFiles: finalEmbeddedFilesInSession,
					totalChunks: this._completedChunks,
					totalSize: this.searchEngine.size(),
					lastUpdated: new Date(),
					languages: new Map()
				};
				this.storageService.store(
					STORAGE_KEY_INDEX_STATS,
					JSON.stringify({
						...statsToStore,
						languages: Array.from(statsToStore.languages.entries())
					}),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);

				// Get final real vector count from server
				try {
					const finalRealVectorCount = await this.getRealVectorCount();
					this._realVectorCount = finalRealVectorCount;
					this.logService.info(`[CodebaseIndexingService] Final vector count from Pinecone (may be eventually consistent): ${finalRealVectorCount}`);
				} catch (error) {
					this.logService.debug('[CodebaseIndexingService] Could not get final vector count:', error);
				}

				// Update final status
				this._currentOverallStatusText = 'Indexing complete';
				this._emitProgressUpdate();

				// Provide more detailed completion message
				const skippedFiles = this._totalFilesToProcess - this._filesRequiringProcessingCount;
				let message = `Indexed ${finalEmbeddedFilesInSession} files this session`;
				if (this._filesWithEmbeddingsErrorsCount > 0) {
					message += ` (${this._filesWithEmbeddingsErrorsCount} files had embedding errors)`;
				}
				if (skippedFiles > 0) {
					message += ` (${skippedFiles} files were already indexed)`;
				}
				if (this._realVectorCount > 0 && this._realVectorCount !== this._completedChunks) {
					message += `. Total ${this._realVectorCount} vectors in cloud (eventually consistent).`;
				}
				this.notificationService.info(message);

			} catch (error) {
				this.logService.error('[CodebaseIndexingService] Indexing error:', error);
				this._currentOverallStatusText = 'Indexing failed';
				this._emitProgressUpdate();
				this.notificationService.error('Indexing failed: ' + (error instanceof Error ? error.message : String(error)));
			} finally {
				cancellationListener.dispose(); // Dispose the listener
				this.cancellationTokenSource?.dispose();
				this.stopIndexing();
			}
		});
	}

	async stopIndexing(): Promise<void> {
		this.logService.info('[CodebaseIndexingService] Stopping indexing...');
		this.cancellationTokenSource?.cancel();
		this.cancellationTokenSource?.dispose();
		this.cancellationTokenSource = undefined;
		this._isIndexing = false;
		this._currentOverallStatusText = 'Idle (Stopped)';
		this._filesRequiringProcessingCount = 0;
		this._filesParsedCount = 0;
		this._filesEmbeddedSuccessfullyCount = 0;
		this._filesWithEmbeddingsErrorsCount = 0;
		this._completedChunks = 0;
		this._totalChunks = 0;
		this._expectedVectorCount = 0;
		this._onDidChangeIndexingStatus.fire(false);
		this._emitProgressUpdate(); // Update UI to reflect stopped state
		this.notificationService.info('Codebase indexing stopped.');
	}

	async clearIndex(): Promise<void> {
		this.logService.info('[CodebaseIndexingService] Clearing index and local state...');
		if (this._isIndexing) {
			await this.stopIndexing();
		}

		// Reset local state
		this.indexedFiles.clear();
		this.parsedFiles.clear();
		this.embeddedFiles.clear();
		this.fileChunks.clear();
		await this.embeddingCache.clear();

		// Clear from localStorage
		this.storageService.remove(STORAGE_KEY_INDEXED_FILES, StorageScope.WORKSPACE);
		this.storageService.remove('codebaseIndexing.embeddedFiles', StorageScope.WORKSPACE);
		this.storageService.remove(STORAGE_KEY_EMBEDDING_CACHE, StorageScope.WORKSPACE);
		this.storageService.remove(STORAGE_KEY_INDEX_STATS, StorageScope.WORKSPACE);

		// Reset progress and counts
		this._totalFilesToProcess = 0;
		this._filesRequiringProcessingCount = 0;
		this._filesParsedCount = 0;
		this._filesEmbeddedSuccessfullyCount = 0;
		this._filesWithEmbeddingsErrorsCount = 0;
		this._completedChunks = 0;
		this._totalChunks = 0;
		this._realVectorCount = 0;
		this._expectedVectorCount = 0;
		this._recentlyProcessedFilesMap.clear();
		this._currentOverallStatusText = 'Idle (Index Cleared)';

		this._emitProgressUpdate(); // Update UI immediately
		this._onDidChangeIndexingStatus.fire(false); // Indexing is not active

		this.notificationService.info('Local index cache cleared. Requesting server to clear...');

		try {
			const requestId = generateUuid();

			// Manually handle response with a Promise
			const responsePromise = new Promise<any>((resolve, reject) => {
				const disposable = this.webSocketService.onMessage((message: any) => {
					if (message.type === MessageType.CODEBASE_CLEAR_INDEX_RESPONSE && message.payload?.requestId === requestId) {
						disposable.dispose();
						resolve(message.payload);
					}
				});

				// Timeout after 30 seconds
				setTimeout(() => {
					disposable.dispose();
					reject(new Error('Clear index request timed out'));
				}, 30000);
			});

			this.webSocketService.sendMessage({
				type: MessageType.CODEBASE_CLEAR_INDEX_REQUEST,
				payload: { requestId }
			});

			const response = await responsePromise;

			if (response.success) {
				this.logService.info(`[CodebaseIndexingService] Server successfully cleared index. Vectors deleted: ${response.deletedVectorCount}`);
				this.notificationService.info('Codebase index cleared successfully on server.');
				this._realVectorCount = 0;
				this.storageService.store(STORAGE_KEY_INDEX_STATS, JSON.stringify({ vectorCount: 0, lastUpdated: Date.now() }), StorageScope.WORKSPACE, StorageTarget.MACHINE);
			} else {
				this.logService.error(`[CodebaseIndexingService] Server failed to clear index: ${response.error}`);
				this.notificationService.error(`Failed to clear codebase index on server: ${response.error}`);
			}
		} catch (error: any) {
			this.logService.error('[CodebaseIndexingService] Error sending clear index request or processing response:', error);
			this.notificationService.error(`Error clearing index: ${error.message}`);
			// If it's a timeout, local cache was still cleared.
			if (error.message === 'Clear index request timed out') {
				this.notificationService.warn('Server clear index request timed out, but local cache was cleared.');
			}
		}
		this._currentOverallStatusText = 'Idle (Index Cleared)';
		this._emitProgressUpdate();
	}

	isIndexing(): boolean {
		return this._isIndexing;
	}

	getProgress(): IIndexingProgress {
		// Determine current phase
		let phase: IIndexingProgress['phase'] = 'idle';
		let currentPercentage = 0;

		if (this._isIndexing) {
			if (this._filesParsedCount < this._filesRequiringProcessingCount && this._filesRequiringProcessingCount > 0) {
				phase = 'parsing';
				currentPercentage = Math.round((this._filesParsedCount / this._filesRequiringProcessingCount) * 100);
			} else if (this._completedChunks < this._totalChunks && this._totalChunks > 0) {
				phase = 'embedding';
				currentPercentage = Math.round((this._completedChunks / this._totalChunks) * 100);
			} else if (this._filesRequiringProcessingCount === 0 && this._totalFilesToProcess > 0 && !this._currentOverallStatusText.startsWith('Collecting')) {
				// All files were already embedded, initial state after file collection
				phase = 'complete';
				currentPercentage = 100;
			} else if (this._filesParsedCount === this._filesRequiringProcessingCount && this._completedChunks === this._totalChunks && this._totalChunks > 0) {
				// All files processed and all chunks embedded
				phase = 'complete';
				currentPercentage = 100;
			} else if (!this._currentOverallStatusText.startsWith('Collecting') && !this._currentOverallStatusText.startsWith('Parsing') && !this._currentOverallStatusText.startsWith('Embedding')){
				phase = 'collecting';
				currentPercentage = 0;
				this._currentOverallStatusText = this._currentOverallStatusText || 'Preparing to index...';
			}
		} else if (!this._isIndexing && this.embeddedFiles.size > 0 && this._totalFilesToProcess === 0 && this._filesRequiringProcessingCount === 0) {
			// Not indexing, but has indexed files from previous session
			phase = 'complete';
			currentPercentage = 100;
			this._currentOverallStatusText = 'Codebase indexed.';
		} else {
			phase = 'idle';
			currentPercentage = 0;
			this._currentOverallStatusText = 'Idle';
		}

		// Get recent files array from map, sorted by timestamp (most recent first)
		const recentlyProcessedFiles = Array.from(this._recentlyProcessedFilesMap.values())
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
			.slice(0, 15); // Limit to last 15 files

		return {
			type: 'progress',
			phase,
			percentage: currentPercentage,
			statusText: this._currentOverallStatusText,
			totalFilesToProcess: this._totalFilesToProcess, // Overall files in workspace matching criteria
			filesParsedCount: this._filesParsedCount, // Parsed in current session
			filesEmbeddedSuccessfullyCount: this._filesEmbeddedSuccessfullyCount, // Embedded in current session
			filesWithEmbeddingsErrorsCount: this._filesWithEmbeddingsErrorsCount, // Errors in current session
			completedChunks: this._completedChunks, // Chunks embedded by server (overall)
			totalChunks: this._totalChunks, // Total chunks for current operation
			recentlyProcessedFiles
		};
	}

	/**
	 * Get the current real vector count from Pinecone
	 */
	getRealVectorCountSync(): number {
		return this._realVectorCount;
	}

	/**
	 * Get the expected vector count (total chunks to be processed)
	 */
	getExpectedVectorCount(): number {
		return this._expectedVectorCount;
	}

	async getIndexStats(): Promise<IIndexStats> {
		// Always try to get real stats from server first
		try {
			const realStats = await this.getRealIndexStatsFromServer();
			if (realStats) {
				// Update stored stats with real data
				this.storageService.store(
					STORAGE_KEY_INDEX_STATS,
					JSON.stringify({
						...realStats,
						languages: Array.from(realStats.languages.entries())
					}),
					StorageScope.WORKSPACE,
					StorageTarget.MACHINE
				);
				return realStats;
			}
		} catch (error) {
			this.logService.debug('[CodebaseIndexingService] Could not get real stats from server:', error);
		}

		// Fallback to stored stats
		const stored = this.storageService.get(STORAGE_KEY_INDEX_STATS, StorageScope.WORKSPACE);
		if (stored) {
			try {
				const parsed = JSON.parse(stored);
				return {
					...parsed,
					lastUpdated: new Date(parsed.lastUpdated),
					languages: new Map(parsed.languages),
					totalSize: this.searchEngine.size() // Always use current search engine size
				};
			} catch (e) {
				// Ignore parse errors
			}
		}

		// Return current state even if no stored stats
		return {
			totalFiles: this.embeddedFiles.size, // Use embedded files for accuracy
			totalChunks: this.searchEngine.size(),
			totalSize: this.searchEngine.size(),
			lastUpdated: new Date(),
			languages: new Map()
		};
	}

	/**
	 * Get real indexing statistics from the server (Pinecone storage)
	 */
	private async getRealIndexStatsFromServer(): Promise<IIndexStats | null> {
		try {
			const requestId = generateUuid();

			// Send request for user namespace stats
			await this.webSocketService.sendMessage({
				type: MessageType.CODEBASE_SEARCH_REQUEST,
				payload: {
					query: '__GET_STATS__', // Special query to get stats
					requestId,
					options: { limit: 0 } // No actual search results needed
				}
			});

			// Wait for response
			return new Promise((resolve) => {
				const disposable = this.webSocketService.onMessage((message: any) => {
					if (message.type === MessageType.CODEBASE_SEARCH_RESPONSE &&
						message.payload?.requestId === requestId) {
						disposable.dispose();

						// If we get stats in the response metadata, use them
						if (message.payload.stats) {
							const stats = message.payload.stats;
							const realStats: IIndexStats = {
								totalFiles: this.embeddedFiles.size, // Use embedded files for accuracy
								totalChunks: stats.vectorCount || 0,
								totalSize: stats.vectorCount || 0,
								lastUpdated: new Date(),
								languages: new Map()
							};

							// Update real vector count for progress calculation
							this._realVectorCount = stats.vectorCount || 0;

							this.logService.info(`[CodebaseIndexingService] Real stats from Pinecone: ${stats.vectorCount} vectors in namespace ${stats.namespace}`);
							resolve(realStats);
						} else {
							resolve(null);
						}
					}
				});

				// Timeout after 10 seconds
				setTimeout(() => {
					disposable.dispose();
					resolve(null);
				}, 10000);
			});

		} catch (error) {
			this.logService.debug('[CodebaseIndexingService] Error getting real stats from server:', error);
			return null;
		}
	}

	/**
	 * Get real-time vector count from server for progress tracking
	 */
	private async getRealVectorCount(): Promise<number> {
		try {
			const stats = await this.getRealIndexStatsFromServer();
			return stats?.totalChunks || 0;
		} catch (error) {
			this.logService.debug('[CodebaseIndexingService] Error getting real vector count:', error);
			return this._realVectorCount; // Return cached value
		}
	}

	/**
	 * Central helper to emit progress updates with current state
	 */
	private _emitProgressUpdate(): void {
		// Determine current phase
		let phase: IIndexingProgress['phase'] = 'idle';
		let currentPercentage = 0;

		if (this._isIndexing) {
			if (this._filesParsedCount < this._filesRequiringProcessingCount && this._filesRequiringProcessingCount > 0) {
				phase = 'parsing';
				currentPercentage = Math.round((this._filesParsedCount / this._filesRequiringProcessingCount) * 100);
			} else if (this._completedChunks < this._totalChunks && this._totalChunks > 0) {
				phase = 'embedding';
				currentPercentage = Math.round((this._completedChunks / this._totalChunks) * 100);
			} else if (this._filesRequiringProcessingCount === 0 && this._totalFilesToProcess > 0 && !this._currentOverallStatusText.startsWith('Collecting')) {
				// All files were already embedded, initial state after file collection
				phase = 'complete';
				currentPercentage = 100;
			} else if (this._filesParsedCount === this._filesRequiringProcessingCount && this._completedChunks === this._totalChunks && this._totalChunks > 0) {
				// All files processed and all chunks embedded
				phase = 'complete';
				currentPercentage = 100;
			} else if (!this._currentOverallStatusText.startsWith('Collecting') && !this._currentOverallStatusText.startsWith('Parsing') && !this._currentOverallStatusText.startsWith('Embedding')){
				phase = 'collecting';
				currentPercentage = 0;
				this._currentOverallStatusText = this._currentOverallStatusText || 'Preparing to index...';
			}
		} else if (!this._isIndexing && this.embeddedFiles.size > 0 && this._totalFilesToProcess === 0 && this._filesRequiringProcessingCount === 0) {
			// Not indexing, but has indexed files from previous session
			phase = 'complete';
			currentPercentage = 100;
			this._currentOverallStatusText = 'Codebase indexed.';
		} else {
			phase = 'idle';
			currentPercentage = 0;
			this._currentOverallStatusText = 'Idle';
		}

		// Get recent files array from map, sorted by timestamp (most recent first)
		const recentlyProcessedFiles = Array.from(this._recentlyProcessedFilesMap.values())
			.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
			.slice(0, 15); // Limit to last 15 files

		this._onDidChangeProgress.fire({
			type: 'progress',
			phase,
			percentage: currentPercentage,
			statusText: this._currentOverallStatusText,
			totalFilesToProcess: this._totalFilesToProcess,
			filesParsedCount: this._filesParsedCount,
			filesEmbeddedSuccessfullyCount: this._filesEmbeddedSuccessfullyCount,
			filesWithEmbeddingsErrorsCount: this._filesWithEmbeddingsErrorsCount,
			completedChunks: this._completedChunks,
			totalChunks: this._totalChunks,
			recentlyProcessedFiles
		});
	}

	async search(query: string, options?: ISearchOptions): Promise<ISearchResult[]> {
		try {
			// Use server-side search via WebSocket
			const requestId = generateUuid();

			// Send search request to server
			await this.webSocketService.sendMessage({
				type: MessageType.CODEBASE_SEARCH_REQUEST,
				payload: {
					query,
					requestId,
					options: {
						limit: options?.limit,
						filters: options?.filters
					}
				}
			});

			// Wait for response
			return new Promise((resolve, reject) => {
				const disposable = this.webSocketService.onMessage((message: any) => {
					if (message.type === MessageType.CODEBASE_SEARCH_RESPONSE &&
						message.payload?.requestId === requestId) {
						disposable.dispose();

						if (message.payload.error) {
							this.logService.error(`[CodebaseIndexingService] Search error: ${message.payload.error}`);
							resolve([]);
						} else {
							// Convert server response to ISearchResult format
							const results: ISearchResult[] = message.payload.results.map((result: any) => ({
								chunk: result.chunk,
								score: result.score,
								highlights: result.highlights || []
							}));

							this.logService.info(`[CodebaseIndexingService] Search completed with ${results.length} results`);
							resolve(results);
						}
					}
				});

				// Timeout after 30 seconds
				setTimeout(() => {
					disposable.dispose();
					this.logService.error('[CodebaseIndexingService] Search request timed out');
					resolve([]);
				}, 30000);
			});

		} catch (error) {
			this.logService.error('[CodebaseIndexingService] Search error:', error);
			return [];
		}
	}

	async indexFile(uri: URI): Promise<void> {
		try {
			const chunks = await this.parseFile(uri);
			if (chunks.length > 0) {
				this.parsedFiles.add(uri.fsPath);
				await this.generateEmbeddings(chunks);
				this.fileChunks.set(uri.toString(), chunks);
				this.indexedFiles.add(uri.toString());
				this.embeddedFiles.add(uri.fsPath);
			}
		} catch (error) {
			this.logService.error(`[CodebaseIndexingService] Error indexing file ${uri.fsPath}:`, error);
			throw error;
		}
	}

	async removeFile(uri: URI): Promise<void> {
		const uriStr = uri.toString();
		const filePath = uri.fsPath;
		const chunks = this.fileChunks.get(uriStr);
		if (chunks) {
			// Remove from search engine
			for (const chunk of chunks) {
				this.searchEngine.removeVector(chunk.id);
				await this.embeddingCache.delete(chunk.id);
			}
			this.fileChunks.delete(uriStr);
		}
		this.indexedFiles.delete(uriStr);
		this.parsedFiles.delete(filePath);
		this.embeddedFiles.delete(filePath);
	}

	async isFileIndexed(uri: URI): Promise<boolean> {
		return this.embeddedFiles.has(uri.fsPath);
	}

	private async collectFiles(paths: URI[], excludePatterns: string[], maxFileSize: number): Promise<URI[]> {
		const files: URI[] = [];
		const supportedExtensions = new Set([
			'.ts', '.tsx', '.js', '.jsx',
			'.py', '.java', '.cpp', '.cc', '.cxx', '.c', '.h', '.hpp',
			'.cs', '.go', '.rs', '.php', '.rb', '.swift', '.kt', '.scala'
		]);

		for (const path of paths) {
			try {
				const stat = await this.fileService.stat(path);
				if (stat.isDirectory) {
					// Recursively collect files
					await this.collectFilesRecursive(path, files, excludePatterns, supportedExtensions, maxFileSize);
				} else if (stat.isFile) {
					const ext = '.' + path.path.split('.').pop();
					if (supportedExtensions.has(ext) && stat.size <= maxFileSize) {
						files.push(path);
					}
				}
			} catch (error) {
				this.logService.error(`[CodebaseIndexingService] Error collecting files from ${path.fsPath}:`, error);
			}
		}

		return files;
	}

	private async collectFilesRecursive(
		dir: URI,
		files: URI[],
		excludePatterns: string[],
		supportedExtensions: Set<string>,
		maxFileSize: number
	): Promise<void> {
		try {
			const children = await this.fileService.resolve(dir);
			if (children.children) {
				for (const child of children.children) {
					const childUri = child.resource;

					// Check exclude patterns
					const shouldExclude = excludePatterns.some(pattern => {
						// Simple pattern matching (could be improved with glob)
						return childUri.path.includes(pattern.replace(/\*/g, ''));
					});

					if (shouldExclude) continue;

					if (child.isFile) {
						const ext = '.' + child.name.split('.').pop();
						if (supportedExtensions.has(ext)) {
							try {
								const stat = await this.fileService.stat(childUri);
								if (stat.size <= maxFileSize) {
									files.push(childUri);
								}
							} catch (e) {
								// Ignore stat errors
							}
						}
					} else if (child.isDirectory) {
						await this.collectFilesRecursive(childUri, files, excludePatterns, supportedExtensions, maxFileSize);
					}
				}
			}
		} catch (error) {
			this.logService.error(`[CodebaseIndexingService] Error reading directory ${dir.fsPath}:`, error);
		}
	}

	private async parseFile(uri: URI): Promise<ICodeChunk[]> {
		try {
			const content = await this.fileService.readFile(uri);
			const text = content.value.toString();

			// Try tree-sitter parsing first
			try {
				const chunks = await this.treeSitterService.parseFile(uri, text);
				if (chunks.length > 0) {
					this.logService.debug(`[CodebaseIndexingService] Tree-sitter parsing successful for ${uri.fsPath}: ${chunks.length} chunks`);
					return chunks;
				}
			} catch (treeSitterError) {
				this.logService.warn(`[CodebaseIndexingService] Tree-sitter parsing failed for ${uri.fsPath}, falling back to simple text chunking:`, treeSitterError);
			}

			// Fallback to simple text-based chunking
			return this.createSimpleTextChunks(uri, text);
		} catch (error) {
			this.logService.error(`[CodebaseIndexingService] Error parsing file ${uri.fsPath}:`, error);
			return [];
		}
	}

	/**
	 * Fallback method to create simple text chunks when tree-sitter fails
	 */
	private createSimpleTextChunks(uri: URI, text: string): ICodeChunk[] {
		const chunks: ICodeChunk[] = [];
		const lines = text.split('\n');
		const chunkSize = 50; // Lines per chunk
		const overlap = 5; // Lines of overlap between chunks

		// Get file extension for language detection
		const ext = uri.path.split('.').pop()?.toLowerCase() || '';
		const language = this.getLanguageFromExtension(ext);

		for (let i = 0; i < lines.length; i += chunkSize - overlap) {
			const endLine = Math.min(i + chunkSize, lines.length);
			const chunkLines = lines.slice(i, endLine);
			const chunkText = chunkLines.join('\n');

			if (chunkText.trim().length > 0) {
				chunks.push({
					id: generateUuid(),
					filePath: uri.fsPath,
					content: chunkText,
					startLine: i + 1,
					endLine: endLine,
					type: 'text_chunk',
					name: `Lines ${i + 1}-${endLine}`,
					language: language
				});
			}
		}

		this.logService.info(`[CodebaseIndexingService] Created ${chunks.length} simple text chunks for ${uri.fsPath}`);
		return chunks;
	}

	/**
	 * Map file extensions to language names
	 */
	private getLanguageFromExtension(ext: string): string {
		const languageMap: Record<string, string> = {
			'ts': 'typescript',
			'tsx': 'typescript',
			'js': 'javascript',
			'jsx': 'javascript',
			'py': 'python',
			'java': 'java',
			'cpp': 'cpp',
			'cc': 'cpp',
			'cxx': 'cpp',
			'c': 'c',
			'h': 'c',
			'hpp': 'cpp',
			'cs': 'csharp',
			'go': 'go',
			'rs': 'rust',
			'php': 'php',
			'rb': 'ruby',
			'swift': 'swift',
			'kt': 'kotlin',
			'scala': 'scala'
		};

		return languageMap[ext] || 'text';
	}

	private async generateEmbeddings(chunks: ICodeChunk[]): Promise<void> {
		if (chunks.length === 0) return;

		const requestId = generateUuid();
		const batchId = generateUuid();

		// Get unique file paths from chunks for tracking
		const filePaths = Array.from(new Set(chunks.map(chunk => chunk.filePath)));

		// Mark all files as embedding in the recent files map
		filePaths.forEach(filePath => {
			const fileName = filePath.split('/').pop() || filePath.split('\\').pop() || filePath;
			this._recentlyProcessedFilesMap.set(filePath, {
				filePath,
				fileName,
				status: 'embedding',
				timestamp: new Date()
			});
		});

		// Send batch embedding request
		await this.webSocketService.sendMessage({
			type: MessageType.CODEBASE_EMBEDDING_BATCH_REQUEST,
			payload: {
				chunks,
				requestId,
				batchId
			}
		});

		// Wait for response with progress tracking
		return new Promise((resolve, reject) => {
			let progressDisposable: IDisposable | undefined;
			let responseDisposable: IDisposable | undefined;

			// Handle progress updates from server
			progressDisposable = this.webSocketService.onMessage((message: any) => {
				if (message.type === MessageType.CODEBASE_EMBEDDING_PROGRESS &&
					message.payload?.requestId === requestId) {

					const {
						completedChunks,
						totalChunks,
						currentBatchNumber, // Corrected: was currentBatch
						totalBatches,
						// percentage, // Server percentage might be different from client's phase-based one
						currentFileRelativePath,
						fileStatus,
						fileErrorDetails
					} = message.payload;

					this.logService.info(`[CodebaseIndexingService] Embedding progress from server: ${completedChunks}/${totalChunks} chunks overall - Batch ${currentBatchNumber}/${totalBatches}`);

					// Update overall chunk-level progress based on server
					this._completedChunks = completedChunks;
					this._totalChunks = Math.max(this._totalChunks, totalChunks);

					// Handle file-level progress updates
					if (currentFileRelativePath) {
						const fileName = currentFileRelativePath.split('/').pop() || currentFileRelativePath;

						// Update or create file entry in recent files map
						const existingFile = this._recentlyProcessedFilesMap.get(currentFileRelativePath);
						let newStatus: IRecentFileActivity['status'] = 'embedding'; // Default to embedding
						let newErrorDetails: string | undefined = undefined;

						if (fileStatus === 'file_completed') {
							newStatus = 'indexed';
							// Only increment if it wasn't already counted as an error
							if(existingFile?.status !== 'error') this._filesEmbeddedSuccessfullyCount++;
						} else if (fileStatus === 'file_error') {
							newStatus = 'error';
							newErrorDetails = fileErrorDetails;
							// Only increment if it wasn't already counted as successful or a different error
							if(existingFile?.status !== 'indexed' && existingFile?.status !== 'error') this._filesWithEmbeddingsErrorsCount++;
						} else if (fileStatus === 'embedding_started' || fileStatus === 'embedding_progress') {
							newStatus = 'embedding';
						}

						this._recentlyProcessedFilesMap.set(currentFileRelativePath, {
							filePath: currentFileRelativePath,
							fileName,
							status: newStatus,
							timestamp: new Date(),
							errorDetails: newErrorDetails
						});

						// Update overall status text more dynamically based on server progress
						if (fileStatus === 'file_completed') {
							this._currentOverallStatusText = `Indexed ${this._filesEmbeddedSuccessfullyCount} of ${this._filesRequiringProcessingCount} files for this session`;
						} else if (fileStatus === 'file_error') {
							this._currentOverallStatusText = `Processing files... (${this._filesWithEmbeddingsErrorsCount} errors this session)`;
						} else {
							this._currentOverallStatusText = `Embedding file: ${fileName}`;
						}
					}

					this._emitProgressUpdate();
				}
			});

			// Handle final response for the batch
			responseDisposable = this.webSocketService.onMessage((message: any) => {
				if (message.type === MessageType.CODEBASE_EMBEDDING_BATCH_RESPONSE &&
					message.payload?.requestId === requestId) {

					progressDisposable?.dispose();
					responseDisposable?.dispose();

					const { embeddings, errors, successfullyStored } = message.payload;
					let batchSuccessCount = 0;

					// Process embeddings for this batch
					for (const embedding of embeddings) {
						const chunk = chunks.find(c => c.id === embedding.chunkId);
						if (chunk && embedding.embedding.length > 0) {
							// Cache embedding
							this.embeddingCache.set(embedding.chunkId, embedding.embedding);
							// Add to search engine
							this.searchEngine.addVector(embedding.chunkId, embedding.embedding, {
								filePath: chunk.filePath,
								type: chunk.type,
								name: chunk.name,
								language: chunk.language
							});
							batchSuccessCount++;
						}
					}

					// Log errors from this batch
					if (errors && errors.length > 0) {
						for (const error of errors) {
							this.logService.error(`[CodebaseIndexingService] Embedding error for chunk ${error.chunkId} (batch ${batchId}): ${error.error}`);
							const erroredChunk = chunks.find(c => c.id === error.chunkId);
							if (erroredChunk) {
								const fileName = erroredChunk.filePath.split('/').pop() || erroredChunk.filePath;
								// Update file status in recent files map if not already marked due to progress message
								const existingFile = this._recentlyProcessedFilesMap.get(erroredChunk.filePath);
								if (!existingFile || existingFile.status !== 'error') {
									this._filesWithEmbeddingsErrorsCount++;
									this._recentlyProcessedFilesMap.set(erroredChunk.filePath, {
										filePath: erroredChunk.filePath,
										fileName,
										status: 'error',
										timestamp: new Date(),
										errorDetails: error.error
									});
								}
							}
						}
					}

					this.logService.info(`[CodebaseIndexingService] Batch ${batchId} complete: Successfully processed ${batchSuccessCount}/${chunks.length} chunks from this batch. Server confirms ${successfullyStored} stored in Pinecone for this batch.`);
					// Note: _completedChunks is updated by the server's overall progress message, not here.
					// _filesEmbeddedSuccessfullyCount is also updated by server progress messages.

					this._emitProgressUpdate(); // Update UI with latest batch info and overall progress
					resolve();
				}
			});

			// Increased timeout to 10 minutes for very slow rate limits
			setTimeout(() => {
				progressDisposable?.dispose();
				responseDisposable?.dispose();
				this.logService.error('[CodebaseIndexingService] Embedding request timed out after 10 minutes');
				reject(new Error('Embedding request timed out'));
			}, 600000); // 10 minutes for 5 RPM rate limit
		});
	}
}

// Register the service
registerSingleton(ICodebaseIndexingService, CodebaseIndexingService, InstantiationType.Delayed);