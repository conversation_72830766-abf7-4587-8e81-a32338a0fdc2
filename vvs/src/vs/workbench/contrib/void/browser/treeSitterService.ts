/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *--------------------------------------------------------------------------------------------*/

import { ITreeSitterService as ITreeSitterServiceInterface, ICodeChunk } from '../common/codebaseIndexingTypes.js';
import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { basename } from '../../../../base/common/path.js';
import { generateUuid } from '../../../../base/common/uuid.js';
import { registerSingleton, InstantiationType } from '../../../../platform/instantiation/common/extensions.js';
import { ITreeSitterParserService } from '../../../../editor/common/services/treeSitterParserService.js';
import type * as Parser from '@vscode/tree-sitter-wasm';
import { WebTreeSitterManager } from './webTreeSitter.js';

export const ITreeSitterService = createDecorator<ITreeSitterServiceInterface>('treeSitterService');

interface IParseResult {
	chunks: ICodeChunk[];
	language: string;
}

export class TreeSitterService extends Disposable implements ITreeSitterServiceInterface {
	readonly _serviceBrand: undefined;

	private readonly supportedLanguages = new Set([
		'typescript',
		'javascript',
		'tsx',
		'jsx',
		'python',
		'java',
		'cpp',
		'c',
		'csharp',
		'go',
		'rust',
		'php',
		'ruby',
		'swift',
		'kotlin',
		'scala',
		'html',
		'css',
		'json',
		'yaml',
		'xml',
		'markdown'
	]);

	private webTreeSitterManager: WebTreeSitterManager | undefined;

	constructor(
		@ILogService private readonly logService: ILogService,
		@ITreeSitterParserService private readonly treeSitterParserService: ITreeSitterParserService
	) {
		super();
		this.logService.info('[TreeSitterService] Initialized with supported languages:', Array.from(this.supportedLanguages));

		// Initialize WebTreeSitterManager
		this.webTreeSitterManager = WebTreeSitterManager.getInstance(this.logService);
	}

	async parseFile(uri: URI, content: string): Promise<ICodeChunk[]> {
		const language = this.detectLanguage(uri);
		if (!language || !this.isLanguageSupported(language)) {
			this.logService.warn(`[TreeSitterService] Unsupported language: ${language} for file ${uri.fsPath}`);
			return [];
		}

		// Skip tree-sitter parsing entirely and go straight to regex for now
		// This avoids the WASM loading issues while still providing useful parsing
		this.logService.info(`[TreeSitterService] Using regex parser for ${uri.fsPath} (language: ${language})`);
		try {
			const result = await this.parseWithRegex(uri, content, language);
			this.logService.info(`[TreeSitterService] Parsed ${result.chunks.length} chunks from ${uri.fsPath} using regex`);
			return result.chunks;
		} catch (regexError) {
			this.logService.error(`[TreeSitterService] Error parsing file ${uri.fsPath} with regex:`, regexError);
			return [];
		}
	}

	public async parseWithWebTreeSitter(uri: URI, content: string, languageId: string): Promise<ICodeChunk[]> {
		this.logService.info(`[TreeSitterService] Attempting to parse ${uri.fsPath} with WebTreeSitterManager for language ${languageId} via parseWithWebTreeSitter method`);
		if (!this.webTreeSitterManager) {
			this.logService.error(`[TreeSitterService] WebTreeSitterManager is not available. Cannot parse ${uri.fsPath}`);
			return [];
		}

		let tree: Parser.Tree | undefined;
		try {
			tree = await this.webTreeSitterManager.parse(content, languageId);
			if (tree) {
				this.logService.info(`[TreeSitterService] Successfully parsed ${uri.fsPath} with WebTreeSitterManager`);
				const result = await this.parseWithTreeSitter(uri, content, languageId, tree);
				this.logService.info(`[TreeSitterService] Parsed ${result.chunks.length} chunks from ${uri.fsPath} using WebTreeSitter tree`);
				return result.chunks;
			} else {
				this.logService.warn(`[TreeSitterService] WebTreeSitterManager returned no tree for ${uri.fsPath}`);
				return [];
			}
		} catch (error) {
			this.logService.error(`[TreeSitterService] Error parsing ${uri.fsPath} with WebTreeSitterManager:`, error);
			return [];
		}
	}

	getSupportedLanguages(): string[] {
		return Array.from(this.supportedLanguages);
	}

	isLanguageSupported(language: string): boolean {
		return this.supportedLanguages.has(language);
	}

	private detectLanguage(uri: URI): string | null {
		const filename = basename(uri.fsPath);
		const ext = filename.split('.').pop()?.toLowerCase();

		const extensionMap: Record<string, string> = {
			'ts': 'typescript',
			'tsx': 'tsx',
			'js': 'javascript',
			'jsx': 'jsx',
			'py': 'python',
			'java': 'java',
			'cpp': 'cpp',
			'cc': 'cpp',
			'cxx': 'cpp',
			'c': 'c',
			'h': 'c',
			'hpp': 'cpp',
			'cs': 'csharp',
			'go': 'go',
			'rs': 'rust',
			'php': 'php',
			'rb': 'ruby',
			'swift': 'swift',
			'kt': 'kotlin',
			'scala': 'scala',
			'html': 'html',
			'css': 'css',
			'json': 'json',
			'yaml': 'yaml',
			'yml': 'yaml',
			'xml': 'xml',
			'md': 'markdown'
		};

		return ext ? extensionMap[ext] || null : null;
	}

	private async parseWithTreeSitter(uri: URI, content: string, language: string, tree: Parser.Tree): Promise<IParseResult> {
		const chunks: ICodeChunk[] = [];
		const lines = content.split('\n');

		// Define queries for different languages
		const queries = this.getQueriesForLanguage(language);
		if (!queries) {
			// If no queries defined, fall back to regex
			return this.parseWithRegex(uri, content, language);
		}

		// Walk the tree and extract chunks based on queries
		const rootNode = tree.rootNode;
		this.walkTree(rootNode, uri, lines, chunks, queries, language);

		return { chunks, language };
	}

	private walkTree(
		node: any,
		uri: URI,
		lines: string[],
		chunks: ICodeChunk[],
		queries: Map<string, 'function' | 'class' | 'method' | 'interface' | 'type' | 'variable' | 'import' | 'export'>,
		language: string
	): void {
		const nodeType = node.type;
		const chunkType = queries.get(nodeType);

		if (chunkType) {
			const startLine = node.startPosition.row + 1;
			const endLine = node.endPosition.row + 1;
			const content = lines.slice(startLine - 1, endLine).join('\n');
			const name = this.extractNodeName(node, nodeType, language);

			// Extract documentation and metadata
			const metadata = this.extractMetadata(node, lines, startLine, language);

			chunks.push({
				id: generateUuid(),
				filePath: uri.fsPath,
				startLine,
				endLine,
				content,
				type: chunkType,
				name,
				language,
				metadata
			});
		}

		// Recursively walk children
		for (let i = 0; i < node.childCount; i++) {
			const child = node.child(i);
			if (child) {
				this.walkTree(child, uri, lines, chunks, queries, language);
			}
		}
	}

	private extractNodeName(node: any, nodeType: string, language: string): string | undefined {
		// Try to find identifier child nodes
		for (let i = 0; i < node.childCount; i++) {
			const child = node.child(i);
			if (child && (child.type === 'identifier' || child.type === 'property_identifier')) {
				return child.text;
			}
		}

		// Language-specific name extraction
		if (language === 'typescript' || language === 'javascript') {
			// For functions, classes, etc., the name is usually the first identifier after the keyword
			const identifierNode = node.childForFieldName('name');
			if (identifierNode) {
				return identifierNode.text;
			}
		}

		return undefined;
	}

	private extractMetadata(node: any, lines: string[], startLine: number, language: string): Record<string, any> {
		const metadata: Record<string, any> = {
			signature: lines[startLine - 1].trim()
		};

		// Extract documentation comments
		const docComment = this.extractDocumentation(lines, startLine - 1, language);
		if (docComment) {
			metadata.docstring = docComment;
		}

		// Extract additional metadata based on node type
		if (node.type === 'function_declaration' || node.type === 'method_definition' || node.type === 'function_definition') {
			// Extract parameters
			const params = this.extractParameters(node, language);
			if (params.length > 0) {
				metadata.parameters = params;
			}

			// Extract return type if available
			const returnType = this.extractReturnType(node, language);
			if (returnType) {
				metadata.returnType = returnType;
			}
		}

		// Extract modifiers (public, private, static, etc.)
		const modifiers = this.extractModifiers(node, language);
		if (modifiers.length > 0) {
			metadata.modifiers = modifiers;
		}

		// Extract imports/dependencies
		if (node.type.includes('import')) {
			const importInfo = this.extractImportInfo(node, language);
			if (importInfo) {
				metadata.imports = importInfo;
			}
		}

		return metadata;
	}

	private extractDocumentation(lines: string[], lineIndex: number, language: string): string | null {
		const docPatterns: Record<string, { start: RegExp; line: RegExp; end?: RegExp }> = {
			typescript: {
				start: /^\s*\/\*\*/,
				line: /^\s*\*/,
				end: /^\s*\*\//
			},
			javascript: {
				start: /^\s*\/\*\*/,
				line: /^\s*\*/,
				end: /^\s*\*\//
			},
			python: {
				start: /^\s*"""/,
				line: /^\s*/,
				end: /^\s*"""/
			},
			java: {
				start: /^\s*\/\*\*/,
				line: /^\s*\*/,
				end: /^\s*\*\//
			},
			csharp: {
				start: /^\s*\/\/\//,
				line: /^\s*\/\/\//
			},
			go: {
				start: /^\s*\/\//,
				line: /^\s*\/\//
			},
			rust: {
				start: /^\s*\/\/\//,
				line: /^\s*\/\/\//
			}
		};

		const pattern = docPatterns[language];
		if (!pattern) return null;

		const docLines: string[] = [];
		let i = lineIndex - 1;

		// Look for documentation above the declaration
		while (i >= 0 && lines[i].trim() === '') {
			i--;
		}

		if (i >= 0) {
			// Check for single-line doc pattern (C#, Rust, Go)
			if (!pattern.end) {
				while (i >= 0 && pattern.line.test(lines[i])) {
					docLines.unshift(lines[i].replace(pattern.line, '').trim());
					i--;
				}
			} else {
				// Multi-line doc pattern (JS/TS, Java, Python)
				if (pattern.end.test(lines[i])) {
					docLines.unshift(lines[i].replace(pattern.end, '').trim());
					i--;
					while (i >= 0) {
						if (pattern.start.test(lines[i])) {
							docLines.unshift(lines[i].replace(pattern.start, '').trim());
							break;
						}
						docLines.unshift(lines[i].replace(pattern.line, '').trim());
						i--;
					}
				}
			}
		}

		return docLines.length > 0 ? docLines.join('\n').trim() : null;
	}

	private extractParameters(node: any, language: string): string[] {
		const params: string[] = [];

		// Find parameter list node
		let paramListNode = node.childForFieldName('parameters');
		if (!paramListNode) {
			// Try different field names for different languages
			paramListNode = node.childForFieldName('formal_parameters') ||
							node.childForFieldName('parameter_list');
		}

		if (paramListNode) {
			for (let i = 0; i < paramListNode.childCount; i++) {
				const child = paramListNode.child(i);
				if (child && child.type.includes('parameter')) {
					params.push(child.text);
				}
			}
		}

		return params;
	}

	private extractReturnType(node: any, language: string): string | null {
		// Try to find return type node
		const returnTypeNode = node.childForFieldName('return_type') ||
							   node.childForFieldName('type');

		if (returnTypeNode) {
			return returnTypeNode.text;
		}

		// For TypeScript/JavaScript, check for type annotation
		if (language === 'typescript' || language === 'tsx') {
			for (let i = 0; i < node.childCount; i++) {
				const child = node.child(i);
				if (child && child.type === 'type_annotation') {
					return child.text;
				}
			}
		}

		return null;
	}

	private extractModifiers(node: any, language: string): string[] {
		const modifiers: string[] = [];
		const modifierTypes = ['public', 'private', 'protected', 'static', 'async', 'const', 'readonly', 'abstract', 'final'];

		// Check parent node for modifiers
		const parent = node.parent;
		if (parent) {
			for (let i = 0; i < parent.childCount; i++) {
				const sibling = parent.child(i);
				if (sibling && sibling.endPosition.row <= node.startPosition.row) {
					const text = sibling.text.toLowerCase();
					if (modifierTypes.includes(text)) {
						modifiers.push(text);
					}
				}
			}
		}

		return modifiers;
	}

	private extractImportInfo(node: any, language: string): any {
		const importInfo: any = {};

		// Extract what is being imported
		const specifierNode = node.childForFieldName('specifier') ||
							  node.childForFieldName('name');
		if (specifierNode) {
			importInfo.specifier = specifierNode.text;
		}

		// Extract source/module
		const sourceNode = node.childForFieldName('source') ||
						   node.childForFieldName('module');
		if (sourceNode) {
			importInfo.source = sourceNode.text.replace(/['"]/g, '');
		}

		return Object.keys(importInfo).length > 0 ? importInfo : null;
	}

	private getQueriesForLanguage(language: string): Map<string, 'function' | 'class' | 'method' | 'interface' | 'type' | 'variable' | 'import' | 'export'> | null {
		const queries = new Map<string, 'function' | 'class' | 'method' | 'interface' | 'type' | 'variable' | 'import' | 'export'>();

		switch (language) {
			case 'typescript':
			case 'javascript':
			case 'tsx':
			case 'jsx':
				queries.set('function_declaration', 'function');
				queries.set('function_expression', 'function');
				queries.set('arrow_function', 'function');
				queries.set('method_definition', 'method');
				queries.set('class_declaration', 'class');
				queries.set('interface_declaration', 'interface');
				queries.set('type_alias_declaration', 'type');
				queries.set('import_statement', 'import');
				queries.set('export_statement', 'export');
				queries.set('variable_declaration', 'variable');
				break;
			case 'python':
				queries.set('function_definition', 'function');
				queries.set('class_definition', 'class');
				queries.set('import_statement', 'import');
				queries.set('import_from_statement', 'import');
				break;
			case 'java':
			case 'csharp':
				queries.set('method_declaration', 'method');
				queries.set('class_declaration', 'class');
				queries.set('interface_declaration', 'interface');
				queries.set('import_declaration', 'import');
				break;
			case 'go':
				queries.set('function_declaration', 'function');
				queries.set('method_declaration', 'method');
				queries.set('type_declaration', 'type');
				queries.set('import_declaration', 'import');
				break;
			case 'rust':
				queries.set('function_item', 'function');
				queries.set('struct_item', 'class');
				queries.set('enum_item', 'class');
				queries.set('trait_item', 'interface');
				queries.set('impl_item', 'method');
				queries.set('use_declaration', 'import');
				break;
			default:
				return null;
		}

		return queries;
	}

	private async parseWithRegex(uri: URI, content: string, language: string): Promise<IParseResult> {
		const chunks: ICodeChunk[] = [];
		const lines = content.split('\n');

		switch (language) {
			case 'typescript':
			case 'javascript':
			case 'tsx':
			case 'jsx':
				chunks.push(...this.parseTypeScriptLike(uri, lines, language));
				break;
			case 'python':
				chunks.push(...this.parsePython(uri, lines));
				break;
			case 'java':
			case 'csharp':
				chunks.push(...this.parseJavaLike(uri, lines, language));
				break;
			case 'go':
				chunks.push(...this.parseGo(uri, lines));
				break;
			case 'rust':
				chunks.push(...this.parseRust(uri, lines));
				break;
			default:
				// For unsupported languages, create a single chunk for the whole file
				if (content.trim()) {
					chunks.push({
						id: generateUuid(),
						filePath: uri.fsPath,
						startLine: 1,
						endLine: lines.length,
						content: content,
						type: 'variable',
						language
					});
				}
		}

		return { chunks, language };
	}

	private parseTypeScriptLike(uri: URI, lines: string[], language: string): ICodeChunk[] {
		const chunks: ICodeChunk[] = [];
		const patterns = {
			class: /^\s*(export\s+)?(abstract\s+)?class\s+(\w+)/,
			interface: /^\s*(export\s+)?interface\s+(\w+)/,
			type: /^\s*(export\s+)?type\s+(\w+)/,
			function: /^\s*(export\s+)?(async\s+)?function\s+(\w+)/,
			arrowFunction: /^\s*(export\s+)?const\s+(\w+)\s*=\s*(\(|async)/,
			method: /^\s*(public|private|protected|static|async)?\s*(\w+)\s*\(/,
			import: /^\s*import\s+/,
			export: /^\s*export\s+/
		};

		let currentChunk: ICodeChunk | null = null;
		let braceCount = 0;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const lineNumber = i + 1;

			// Track braces
			braceCount += (line.match(/{/g) || []).length;
			braceCount -= (line.match(/}/g) || []).length;

			// Check for new chunks
			for (const [type, pattern] of Object.entries(patterns)) {
				const match = line.match(pattern);
				if (match) {
					// Save previous chunk if exists
					if (currentChunk && currentChunk.content.trim()) {
						chunks.push(currentChunk);
					}

					const name = match[3] || match[2] || undefined;
					currentChunk = {
						id: generateUuid(),
						filePath: uri.fsPath,
						startLine: lineNumber,
						endLine: lineNumber,
						content: line,
						type: type as any,
						name,
						language,
						metadata: {
							signature: line.trim()
						}
					};
					break;
				}
			}

			// Continue building current chunk
			if (currentChunk) {
				if (lineNumber > currentChunk.startLine) {
					currentChunk.content += '\n' + line;
					currentChunk.endLine = lineNumber;
				}

				// End chunk if we're back to base indentation or found a new declaration
				if (braceCount === 0 && line.trim() === '') {
					chunks.push(currentChunk);
					currentChunk = null;
				}
			}
		}

		// Don't forget the last chunk
		if (currentChunk && currentChunk.content.trim()) {
			chunks.push(currentChunk);
		}

		return chunks;
	}

	private parsePython(uri: URI, lines: string[]): ICodeChunk[] {
		const chunks: ICodeChunk[] = [];
		const patterns = {
			class: /^\s*class\s+(\w+)/,
			function: /^\s*def\s+(\w+)/,
			asyncFunction: /^\s*async\s+def\s+(\w+)/,
			import: /^\s*(from\s+\S+\s+)?import\s+/
		};

		let currentChunk: ICodeChunk | null = null;
		let baseIndent = 0;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const lineNumber = i + 1;
			const indent = line.search(/\S/);

			// Check for new chunks
			for (const [type, pattern] of Object.entries(patterns)) {
				const match = line.match(pattern);
				if (match) {
					// Save previous chunk if exists
					if (currentChunk && currentChunk.content.trim()) {
						chunks.push(currentChunk);
					}

					const name = match[1];
					baseIndent = indent === -1 ? 0 : indent;
					currentChunk = {
						id: generateUuid(),
						filePath: uri.fsPath,
						startLine: lineNumber,
						endLine: lineNumber,
						content: line,
						type: type === 'asyncFunction' ? 'function' : type as any,
						name,
						language: 'python',
						metadata: {
							signature: line.trim()
						}
					};
					break;
				}
			}

			// Continue building current chunk
			if (currentChunk && lineNumber > currentChunk.startLine) {
				// Check if we should end the chunk
				if (indent !== -1 && indent <= baseIndent && line.trim() !== '') {
					chunks.push(currentChunk);
					currentChunk = null;
					// Re-process this line as it might be a new chunk
					i--;
				} else {
					currentChunk.content += '\n' + line;
					currentChunk.endLine = lineNumber;
				}
			}
		}

		// Don't forget the last chunk
		if (currentChunk && currentChunk.content.trim()) {
			chunks.push(currentChunk);
		}

		return chunks;
	}

	private parseJavaLike(uri: URI, lines: string[], language: string): ICodeChunk[] {
		const chunks: ICodeChunk[] = [];
		const patterns = {
			class: /^\s*(public|private|protected)?\s*(abstract|final)?\s*class\s+(\w+)/,
			interface: /^\s*(public|private|protected)?\s*interface\s+(\w+)/,
			method: /^\s*(public|private|protected)?\s*(static)?\s*(\w+\s+)?(\w+)\s*\(/,
			import: /^\s*import\s+/
		};

		let currentChunk: ICodeChunk | null = null;
		let braceCount = 0;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const lineNumber = i + 1;

			// Track braces
			braceCount += (line.match(/{/g) || []).length;
			braceCount -= (line.match(/}/g) || []).length;

			// Check for new chunks
			for (const [type, pattern] of Object.entries(patterns)) {
				const match = line.match(pattern);
				if (match) {
					// Save previous chunk if exists
					if (currentChunk && currentChunk.content.trim()) {
						chunks.push(currentChunk);
					}

					const name = match[4] || match[3] || match[2] || undefined;
					currentChunk = {
						id: generateUuid(),
						filePath: uri.fsPath,
						startLine: lineNumber,
						endLine: lineNumber,
						content: line,
						type: type as any,
						name,
						language,
						metadata: {
							signature: line.trim()
						}
					};
					break;
				}
			}

			// Continue building current chunk
			if (currentChunk) {
				if (lineNumber > currentChunk.startLine) {
					currentChunk.content += '\n' + line;
					currentChunk.endLine = lineNumber;
				}

				// End chunk if we're back to base level
				if (braceCount === 0 && line.includes('}')) {
					chunks.push(currentChunk);
					currentChunk = null;
				}
			}
		}

		// Don't forget the last chunk
		if (currentChunk && currentChunk.content.trim()) {
			chunks.push(currentChunk);
		}

		return chunks;
	}

	private parseGo(uri: URI, lines: string[]): ICodeChunk[] {
		const chunks: ICodeChunk[] = [];
		const patterns = {
			function: /^\s*func\s+(\w+)/,
			method: /^\s*func\s+\(\w+\s+\*?\w+\)\s+(\w+)/,
			type: /^\s*type\s+(\w+)/,
			import: /^\s*import\s+/
		};

		let currentChunk: ICodeChunk | null = null;
		let braceCount = 0;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const lineNumber = i + 1;

			// Track braces
			braceCount += (line.match(/{/g) || []).length;
			braceCount -= (line.match(/}/g) || []).length;

			// Check for new chunks
			for (const [type, pattern] of Object.entries(patterns)) {
				const match = line.match(pattern);
				if (match) {
					// Save previous chunk if exists
					if (currentChunk && currentChunk.content.trim()) {
						chunks.push(currentChunk);
					}

					const name = match[1];
					currentChunk = {
						id: generateUuid(),
						filePath: uri.fsPath,
						startLine: lineNumber,
						endLine: lineNumber,
						content: line,
						type: type as any,
						name,
						language: 'go',
						metadata: {
							signature: line.trim()
						}
					};
					break;
				}
			}

			// Continue building current chunk
			if (currentChunk) {
				if (lineNumber > currentChunk.startLine) {
					currentChunk.content += '\n' + line;
					currentChunk.endLine = lineNumber;
				}

				// End chunk if we're back to base level
				if (braceCount === 0 && line.includes('}')) {
					chunks.push(currentChunk);
					currentChunk = null;
				}
			}
		}

		// Don't forget the last chunk
		if (currentChunk && currentChunk.content.trim()) {
			chunks.push(currentChunk);
		}

		return chunks;
	}

	private parseRust(uri: URI, lines: string[]): ICodeChunk[] {
		const chunks: ICodeChunk[] = [];
		const patterns = {
			function: /^\s*(pub\s+)?fn\s+(\w+)/,
			struct: /^\s*(pub\s+)?struct\s+(\w+)/,
			enum: /^\s*(pub\s+)?enum\s+(\w+)/,
			trait: /^\s*(pub\s+)?trait\s+(\w+)/,
			impl: /^\s*impl\s+/,
			use: /^\s*use\s+/
		};

		let currentChunk: ICodeChunk | null = null;
		let braceCount = 0;

		for (let i = 0; i < lines.length; i++) {
			const line = lines[i];
			const lineNumber = i + 1;

			// Track braces
			braceCount += (line.match(/{/g) || []).length;
			braceCount -= (line.match(/}/g) || []).length;

			// Check for new chunks
			for (const [type, pattern] of Object.entries(patterns)) {
				const match = line.match(pattern);
				if (match) {
					// Save previous chunk if exists
					if (currentChunk && currentChunk.content.trim()) {
						chunks.push(currentChunk);
					}

					const name = match[2] || undefined;
					const chunkType = type === 'struct' || type === 'enum' || type === 'trait' ? 'class' :
						type === 'use' ? 'import' : type as any;

					currentChunk = {
						id: generateUuid(),
						filePath: uri.fsPath,
						startLine: lineNumber,
						endLine: lineNumber,
						content: line,
						type: chunkType,
						name,
						language: 'rust',
						metadata: {
							signature: line.trim()
						}
					};
					break;
				}
			}

			// Continue building current chunk
			if (currentChunk) {
				if (lineNumber > currentChunk.startLine) {
					currentChunk.content += '\n' + line;
					currentChunk.endLine = lineNumber;
				}

				// End chunk if we're back to base level
				if (braceCount === 0 && line.includes('}')) {
					chunks.push(currentChunk);
					currentChunk = null;
				}
			}
		}

		// Don't forget the last chunk
		if (currentChunk && currentChunk.content.trim()) {
			chunks.push(currentChunk);
		}

		return chunks;
	}
}

// Register the service
registerSingleton(ITreeSitterService, TreeSitterService, InstantiationType.Delayed);