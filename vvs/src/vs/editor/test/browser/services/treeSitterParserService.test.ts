/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/
import assert from 'assert';
import { ensureNoDisposablesAreLeakedInTestSuite } from '../../../../base/test/common/utils.js';
import type * as Parser from '@vscode/tree-sitter-wasm';
import { createTextModel } from '../../common/testTextModel.js';
import { timeout } from '../../../../base/common/async.js';
import { <PERSON><PERSON><PERSON>, ILogService } from '../../../../platform/log/common/log.js';
import { ITelemetryService } from '../../../../platform/telemetry/common/telemetry.js';
import { LogService } from '../../../../platform/log/common/logService.js';
import { mock } from '../../../../base/test/common/mock.js';
import { IFileService } from '../../../../platform/files/common/files.js';
import { ITreeSitterImporter } from '../../../common/services/treeSitterParserService.js';
import { TextModelTreeSitter } from '../../../common/services/treeSitter/textModelTreeSitter.js';
import { TreeSitterLanguages } from '../../../common/services/treeSitter/treeSitterLanguages.js';
import { IEnvironmentService, INativeEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { InstantiationService } from '../../../../platform/instantiation/common/instantiationService.js';
import { ServiceCollection } from '../../../../platform/instantiation/common/serviceCollection.js';
import { ITreeSitterParserLoader } from '../../../common/services/treeSitter/treeSitterParserLoader.js';
import { URI } from '../../../../base/common/uri.js';

class MockParser implements Parser.Parser {
	language: Parser.Language | null = null;
	delete(): void { }
	setLanguage(language: Parser.Language | null) { return this; }
	parse(callback: string | Parser.ParseCallback, oldTree?: Parser.Tree | null, options?: Parser.ParseOptions): Parser.Tree | null {
		return new MockTree();
	}
	reset(): void { }
	getIncludedRanges(): Parser.Range[] {
		return [];
	}
	getTimeoutMicros(): number { return 0; }
	setTimeoutMicros(timeout: number): void { }
	setLogger(callback: Parser.LogCallback | boolean | null): this {
		throw new Error('Method not implemented.');
	}
	getLogger(): Parser.LogCallback | null {
		throw new Error('Method not implemented.');
	}
}

class MockTreeSitterImporter implements ITreeSitterImporter {
	_serviceBrand: undefined;
	async getParserClass(): Promise<typeof Parser.Parser> {
		return MockParser as any;
	}
	async getLanguageClass(): Promise<typeof Parser.Language> {
		return MockLanguage as any;
	}
	async getQueryClass(): Promise<typeof Parser.Query> {
		throw new Error('Method not implemented.');
	}
	parserClass = MockParser as any;
}

class MockTree implements Parser.Tree {
	language: Parser.Language = new MockLanguage();
	editorLanguage: string = '';
	editorContents: string = '';
	rootNode: Parser.Node = {} as any;
	rootNodeWithOffset(offsetBytes: number, offsetExtent: Parser.Point): Parser.Node {
		throw new Error('Method not implemented.');
	}
	copy(): Parser.Tree {
		throw new Error('Method not implemented.');
	}
	delete(): void { }
	edit(edit: Parser.Edit): Parser.Tree {
		return this;
	}
	walk(): Parser.TreeCursor {
		throw new Error('Method not implemented.');
	}
	getChangedRanges(other: Parser.Tree): Parser.Range[] {
		throw new Error('Method not implemented.');
	}
	getIncludedRanges(): Parser.Range[] {
		throw new Error('Method not implemented.');
	}
	getEditedRange(other: Parser.Tree): Parser.Range {
		throw new Error('Method not implemented.');
	}
	getLanguage(): Parser.Language {
		throw new Error('Method not implemented.');
	}
}

class MockLanguage implements Parser.Language {
	types: string[] = [];
	fields: (string | null)[] = [];
	get name(): string | null {
		throw new Error('Method not implemented.');
	}
	get abiVersion(): number {
		throw new Error('Method not implemented.');
	}
	get metadata(): Parser.LanguageMetadata | null {
		throw new Error('Method not implemented.');
	}
	get supertypes(): number[] {
		throw new Error('Method not implemented.');
	}
	subtypes(supertype: number): number[] {
		throw new Error('Method not implemented.');
	}
	version: number = 0;
	fieldCount: number = 0;
	stateCount: number = 0;
	nodeTypeCount: number = 0;
	fieldNameForId(fieldId: number): string | null {
		throw new Error('Method not implemented.');
	}
	fieldIdForName(fieldName: string): number | null {
		throw new Error('Method not implemented.');
	}
	idForNodeType(type: string, named: boolean): number {
		throw new Error('Method not implemented.');
	}
	nodeTypeForId(typeId: number): string | null {
		throw new Error('Method not implemented.');
	}
	nodeTypeIsNamed(typeId: number): boolean {
		throw new Error('Method not implemented.');
	}
	nodeTypeIsVisible(typeId: number): boolean {
		throw new Error('Method not implemented.');
	}
	nextState(stateId: number, typeId: number): number {
		throw new Error('Method not implemented.');
	}
	query(source: string): Parser.Query {
		throw new Error('Method not implemented.');
	}
	lookaheadIterator(stateId: number): Parser.LookaheadIterator | null {
		throw new Error('Method not implemented.');
	}
	languageId: string = '';
}

suite('TreeSitterParserService', function () {
	let logService: ILogService;
	let telemetryService: ITelemetryService;
	let instantiationService: InstantiationService;
	let environmentServiceMock: IEnvironmentService & Partial<INativeEnvironmentService>;
	let mockParserLoaderInstance: ITreeSitterParserLoader;
	let fileServiceMock: IFileService;

	const treeSitterImporter = new MockTreeSitterImporter();

	setup(function () {
		const services = new ServiceCollection();
		logService = new LogService(new ConsoleMainLogger());
		telemetryService = new class extends mock<ITelemetryService>() {
			override async publicLog2() {
				//
			}
		};

		environmentServiceMock = {
			_serviceBrand: undefined,
			isBuilt: false,
			userRoamingDataHome: URI.file('/mock/userRoamingDataHome'),
			logsHome: URI.file('/mock/logsHome'),
			cacheHome: URI.file('/mock/cacheHome'),
			isExtensionDevelopment: false,
			extensionsPath: '/mock/extensions',
			appRoot: '/mock/appRoot',
			extensionDevelopmentLocationURI: [URI.file('/mock/extensionDevelopmentLocationURI')],
			extensionTestsLocationURI: URI.file('/mock/extensionTestsLocationURI'),
			disableExtensions: false,
			debugExtensionHost: {} as any,
			verbose: false,
			disableTelemetry: false,
			logLevel: undefined,
			userDataPath: '/mock/userDataPath',
			workspaceStorageHome: URI.file('/mock/workspaceStorageHome'),
			userDataSyncHome: URI.file('/mock/userDataSyncHome'),
			stateResource: URI.file('/mock/stateResource'),
			keyboardLayoutResource: URI.file('/mock/keyboardLayoutResource'),
			argvResource: URI.file('/mock/argvResource'),
			untitledWorkspacesHome: URI.file('/mock/untitledWorkspacesHome'),
			localHistoryHome: URI.file('/mock/localHistoryHome'),
			sync: undefined,
			serviceMachineIdResource: URI.file('/mock/serviceMachineIdResource'),
			userHome: URI.file('/mock/userHome'),
			appSettingsHome: URI.file('/mock/appSettingsHome'),
			tmpDir: URI.file('/mock/tmpDir'),
			machineSettingsResource: URI.file('/mock/machineSettingsResource'),
			extensionsDownloadLocation: URI.file('/mock/extensionsDownloadLocation'),
			builtinExtensionsPath: '/mock/builtinExtensionsPath',
		};

		mockParserLoaderInstance = new class extends mock<ITreeSitterParserLoader>() {
			override parserClass?: typeof Parser | undefined = MockParser as any;
			override async loadParser(wasmUri: URI, languageId: string): Promise<typeof Parser | undefined> { return MockParser as any; }
			override _serviceBrand: undefined;
		};

		fileServiceMock = {
			_serviceBrand: undefined,
			async exists(resource: URI): Promise<boolean> { return false; },
			async readFile(resource: URI) { return { value: new Uint8Array() }; },
		} as any;

		services.set(ILogService, logService);
		services.set(ITelemetryService, telemetryService);
		services.set(IEnvironmentService, environmentServiceMock);
		services.set(ITreeSitterParserLoader, mockParserLoaderInstance);
		services.set(IFileService, fileServiceMock);
		services.set(ITreeSitterImporter, treeSitterImporter);

		instantiationService = new InstantiationService(services, true);
	});

	const store = ensureNoDisposablesAreLeakedInTestSuite();

	test('TextModelTreeSitter race condition: first language is slow to load', async function () {
		const registeredLanguagesMap = new Map<string, string>();
		const treeSitterLanguages: TreeSitterLanguages = store.add(
			instantiationService.createInstance(TreeSitterLanguages, environmentServiceMock, registeredLanguagesMap)
		);

		const textModel = store.add(createTextModel('console.log("Hello, world!");', 'javascript'));
		const textModelTreeSitter = store.add(instantiationService.createInstance(TextModelTreeSitter, textModel, treeSitterLanguages, false));

		textModel.setLanguage('typescript');
		await timeout(300);
		assert.strictEqual((textModelTreeSitter.parseResult?.language as MockLanguage).languageId, 'typescript');
	});

	test('TreeSitterLanguages instantiation', async function () {
		const registeredLanguages = new Map<string, string>();
		registeredLanguages.set('typescript', 'tree-sitter-typescript');

		const languages = instantiationService.createInstance(TreeSitterLanguages, environmentServiceMock, registeredLanguages);

		assert.ok(languages);
		const lang = await languages.getLanguage('typescript');
		assert.ok(lang);
	});
});
