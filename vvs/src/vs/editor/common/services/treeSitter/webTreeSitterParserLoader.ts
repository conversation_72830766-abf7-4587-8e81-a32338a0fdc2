/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved. Copyright (c) 2025 Coode AI Editor
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
// IEnvironmentService is no longer needed as getWasmPath is static
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

// Note: This needs to be imported without type checking as it's an external module
// that will be loaded dynamically
let Parser: any;
let Language: any;

export class WebTreeSitterParserLoader implements ITreeSitterParserLoader {
    readonly _serviceBrand: undefined;
    
    private _initialized = false;
    private _initPromise: Promise<void> | undefined;
    public parserClass: any;

    constructor(
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.info('[WebTreeSitterParserLoader] Constructor called');
    }

    private async initialize(): Promise<void> {
        if (this._initialized) {
            return;
        }

        if (this._initPromise) {
            return this._initPromise;
        }

        this._initPromise = (async () => {
            try {
                // Dynamically import web-tree-sitter
                const webTreeSitter = await import('web-tree-sitter');
                Parser = webTreeSitter.Parser;
                Language = webTreeSitter.Language;

                // Initialize the Parser
                await Parser.init({
                    locateFile: (scriptName: string) => {
                        // Return the path to the wasm file
                        // In a browser environment, this should be a URL path
                        const wasmPath = this.getWasmPath();
                        this._logService.info(`[WebTreeSitterParserLoader] Locating file: ${scriptName} at ${wasmPath}`);
                        return `${wasmPath}/tree-sitter.wasm`;
                    }
                });

                this._logService.info('[WebTreeSitterParserLoader] Parser initialized successfully');
                
                // Set parserClass for access by other services
                this.parserClass = {
                    Parser,
                    Language
                };
                
                this._initialized = true;
            } catch (error) {
                this._logService.error('[WebTreeSitterParserLoader] Failed to initialize web-tree-sitter:', error);
                throw error;
            }
        })();

        return this._initPromise;
    }

    private getWasmPath(): string {
        // All WASM files are served from /tree-sitter-wasm/ at the web root.
        // This path is where the setup-tree-sitter-wasm.sh script places them.
        return '/tree-sitter-wasm';
    }

    async loadParser(wasmUri: URI, languageId: string): Promise<any> {
        await this.initialize();

        if (languageId === 'core') {
            this._logService.info('[WebTreeSitterParserLoader] Returning core parser');
            return this.parserClass;
        }

        // For non-core parsers, we still return the core parser
        // The actual language-specific WASM files will be loaded by TreeSitterLanguages
        this._logService.info(`[WebTreeSitterParserLoader] Returning parser for ${languageId}`);
        return this.parserClass;
    }
}
