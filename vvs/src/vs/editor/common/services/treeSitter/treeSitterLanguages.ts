/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type { Language as TreeSitterLanguageType } from '@vscode/tree-sitter-wasm';
import { Disposable } from '../../../../base/common/lifecycle.js';
import { Emitter, Event } from '../../../../base/common/event.js';
import { IEnvironmentService } from '../../../../platform/environment/common/environment.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

export function getModuleLocation(environmentService: IEnvironmentService): string {
	if (environmentService.isBuilt && !environmentService.isExtensionDevelopment) {
		return '../../../../../node_modules/@vscode/tree-sitter-wasm/wasm';
	}
	if (typeof window !== 'undefined') {
		return 'node_modules/@vscode/tree-sitter-wasm/wasm';
	}
	return './node_modules/@vscode/tree-sitter-wasm/wasm';
}

export class TreeSitterLanguages extends Disposable {
	private readonly _onDidAddLanguage = this._register(new Emitter<{ id: string; language: TreeSitterLanguageType }>());
	public readonly onDidAddLanguage: Event<{ id: string; language: TreeSitterLanguageType }> = this._onDidAddLanguage.event;

	private readonly _languages: Map<string, TreeSitterLanguageType | Promise<TreeSitterLanguageType>> = new Map();
	private readonly _wasmPath: string;

	constructor(
		@IEnvironmentService private readonly _environmentService: IEnvironmentService,
		private readonly _registeredLanguages: Map<string, string>, // languageId -> grammarName (e.g., 'tree-sitter-typescript.wasm')
		@ILogService private readonly _logService: ILogService,
		@ITreeSitterParserLoader private readonly _parserLoader: ITreeSitterParserLoader,
	) {
		super();
		this._wasmPath = getModuleLocation(this._environmentService);
		this._logService.trace('TreeSitterLanguages wasm path:', this._wasmPath);
	}

	public getOrInitLanguage(languageId: string): TreeSitterLanguageType | undefined {
		const result = this._languages.get(languageId);
		if (!result || result instanceof Promise) {
			return undefined;
		}
		return result;
	}

	public getLanguage(languageId: string): TreeSitterLanguageType | undefined | Promise<TreeSitterLanguageType> {
		if (!this._registeredLanguages.has(languageId)) {
			return undefined;
		}
		return this._languages.get(languageId) ?? this._addLanguage(languageId);
	}

	private _addLanguage(languageId: string): Promise<TreeSitterLanguageType> {
		const grammarName = this._registeredLanguages.get(languageId);
		if (!grammarName) {
			const msg = `No grammar name registered for languageId: ${languageId}`;
			this._logService.error(msg);
			return Promise.reject(new Error(msg));
		}
		const promise = this._fetchLanguage(languageId, grammarName);
		this._languages.set(languageId, promise);
		promise.then(lang => {
			this._languages.set(languageId, lang);
			this._onDidAddLanguage.fire({ id: languageId, language: lang });
		}).catch(err => {
			this._languages.delete(languageId);
			this._logService.error(`Failed to load Tree Sitter language ${languageId} (${grammarName}):`, err);
		});
		return promise;
	}

	private async _fetchLanguage(languageId: string, grammarName: string): Promise<TreeSitterLanguageType> {
		let ParserWasmNamespace = this._parserLoader.parserClass;
		if (!ParserWasmNamespace) {
			if (typeof this._parserLoader.parserClass === 'function') {
				ParserWasmNamespace = await (this._parserLoader.parserClass as any)();
			} else {
				ParserWasmNamespace = this._parserLoader.parserClass;
			}

			if (!ParserWasmNamespace) {
				this._logService.warn('[TreeSitterLanguages] _parserLoader.parserClass is not set. Attempting to load core parser. This might be an issue.');
				throw new Error('Tree Sitter Parser class not available via _parserLoader.parserClass.');
			}
		}

		if (!ParserWasmNamespace.Language) {
			throw new Error('Tree Sitter Language class not available from ParserWasmNamespace.');
		}

		let wasmBytes: ArrayBuffer;
		let wasmModuleUrl: string;

		if (this._environmentService.isBuilt && !this._environmentService.isExtensionDevelopment) {
			wasmModuleUrl = `${this._wasmPath}/${grammarName}.wasm`;
			this._logService.debug(`[TreeSitterLanguages _fetchLanguage] Loading WASM for ${languageId} from URL (production/built): ${wasmModuleUrl}`);
		} else {
			wasmModuleUrl = `${this._wasmPath}/${grammarName}.wasm`;
			if (typeof window !== 'undefined') {
				// Simple check for browser
			} else {
				this._logService.warn(`[TreeSitterLanguages _fetchLanguage] Attempting to fetch local WASM in non-browser env for ${languageId}: ${wasmModuleUrl}. This might fail if not served.`);
			}
			this._logService.debug(`[TreeSitterLanguages _fetchLanguage] Loading WASM for ${languageId} from URL (development): ${wasmModuleUrl}`);
		}

		const response = await fetch(wasmModuleUrl);
		if (!response.ok) {
			const errorMsg = `Failed to fetch ${grammarName}.wasm from ${wasmModuleUrl}: ${response.status} ${response.statusText}`;
			this._logService.error(errorMsg);
			throw new Error(errorMsg);
		}
		wasmBytes = await response.arrayBuffer();

		this._logService.debug(`[TreeSitterLanguages _fetchLanguage] WASM bytes fetched for ${languageId}, length: ${wasmBytes.byteLength}`);
		const lang = await ParserWasmNamespace.Language.load(new Uint8Array(wasmBytes));
		(lang as any).languageId = languageId; // Tag it for easier debugging if needed
		this._logService.info(`[TreeSitterLanguages _fetchLanguage] Tree Sitter language ${languageId} (${grammarName}) loaded successfully.`);
		return lang;
	}
}
