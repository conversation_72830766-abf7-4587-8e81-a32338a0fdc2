/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type * as Parser from '@vscode/tree-sitter-wasm';
import { URI } from '../../../../base/common/uri.js';
import { createDecorator } from '../../../../platform/instantiation/common/instantiation.js';

export const ITreeSitterParserLoader = createDecorator<ITreeSitterParserLoader>('treeSitterParserLoader');

export interface ITreeSitterParserLoader {
	_serviceBrand: undefined; // Required for service identifiers
    loadParser(wasmUri: URI, languageId: string): Promise<typeof Parser | undefined>;
    parserClass?: typeof Parser;
}