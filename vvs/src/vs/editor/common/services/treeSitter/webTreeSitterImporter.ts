/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterImporter } from '../treeSitterParserService.js';

export class WebTreeSitterImporter implements ITreeSitterImporter {
    readonly _serviceBrand: undefined;
    private _treeSitterImportPromise: Promise<any> | undefined;
    private _initialized: boolean = false;
    private _parserClass: any;

    constructor(
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.info('[WebTreeSitterImporter] Constructor called');
    }

    private async _initialize(): Promise<void> {
        if (this._initialized) {
            return;
        }

        try {
            // Dynamically import web-tree-sitter
            const webTreeSitter = await import('web-tree-sitter');

            // Initialize the Parser
            await webTreeSitter.Parser.init({
                locateFile: (scriptName: string) => {
                    // Return the path to the wasm file
                    this._logService.info(`[WebTreeSitterImporter] Locating file: ${scriptName}`);
                    return `node_modules/web-tree-sitter/${scriptName}`;
                }
            });

            this._logService.info('[WebTreeSitterImporter] Parser initialized successfully');
            this._initialized = true;
        } catch (error) {
            this._logService.error('[WebTreeSitterImporter] Failed to initialize web-tree-sitter:', error);
            throw error;
        }
    }

    private _getTreeSitterImport(): Promise<any> {
        if (!this._treeSitterImportPromise) {
            this._treeSitterImportPromise = (async () => {
                await this._initialize();
                try {
                    const webTreeSitter = await import('web-tree-sitter');
                    this._logService.info('[WebTreeSitterImporter] Successfully imported web-tree-sitter');
                    return webTreeSitter;
                } catch (err) {
                    this._logService.error('[WebTreeSitterImporter] Failed to import web-tree-sitter:', err);
                    throw err;
                }
            })();
        }
        return this._treeSitterImportPromise;
    }

    public async getParserClass(): Promise<any> {
        const importedModule = await this._getTreeSitterImport();
        if (!importedModule || !importedModule.Parser) {
            this._logService.error('[WebTreeSitterImporter] Parser class not found in imported module');
            throw new Error('TreeSitter module or Parser class not loaded');
        }
        this._logService.info('[WebTreeSitterImporter] Successfully retrieved Parser class');
        this._parserClass = importedModule.Parser;
        return importedModule.Parser;
    }

    public async getLanguageClass(): Promise<any> {
        const importedModule = await this._getTreeSitterImport();
        if (!importedModule || !importedModule.Language) {
            this._logService.error('[WebTreeSitterImporter] Language class not found in imported module');
            throw new Error('TreeSitter module or Language class not loaded');
        }
        this._logService.info('[WebTreeSitterImporter] Successfully retrieved Language class');
        return importedModule.Language;
    }

    public async getQueryClass(): Promise<any> {
        const importedModule = await this._getTreeSitterImport();
        if (!importedModule || !importedModule.Query) {
            this._logService.error('[WebTreeSitterImporter] Query class not found in imported module');
            throw new Error('TreeSitter module or Query class not loaded');
        }
        this._logService.info('[WebTreeSitterImporter] Successfully retrieved Query class');
        return importedModule.Query;
    }

    get parserClass(): any {
        return this._parserClass;
    }
}
