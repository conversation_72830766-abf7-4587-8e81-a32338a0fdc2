/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type * as ParserWasmNamespace from '@vscode/tree-sitter-wasm';
import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterImporter } from '../treeSitterParserService.js';
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

export class TreeSitterParserLoaderImpl implements ITreeSitterParserLoader {
    readonly _serviceBrand: undefined;

    private _parserModulePromise: Promise<typeof ParserWasmNamespace | undefined> | undefined;
    public parserClass?: typeof ParserWasmNamespace;

    constructor(
        @ITreeSitterImporter private readonly _treeSitterImporter: ITreeSitterImporter,
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.trace('[TreeSitterParserLoaderImpl] constructor');
    }

    async loadParser(wasmUri: URI, languageId: string): Promise<typeof ParserWasmNamespace | undefined> {
        this._logService.info(`[TreeSitterParserLoaderImpl] loadParser called for languageId: ${languageId}, wasmUri: ${wasmUri.toString()}`);

        if (languageId === 'core') {
            if (!this._parserModulePromise) {
                this._parserModulePromise = (async () => {
                    try {
                        // Attempt to import the module directly.
                        const fullModule = await import('@vscode/tree-sitter-wasm');
                        if (fullModule && fullModule.Parser && fullModule.Language) {
                            this._logService.info('[TreeSitterParserLoaderImpl] Core parser module loaded successfully via direct import.');
                            this.parserClass = fullModule;
                            return fullModule;
                        }
                    } catch (err) {
                        this._logService.warn('[TreeSitterParserLoaderImpl] Direct import of @vscode/tree-sitter-wasm failed. Attempting fallback.', err);
                        // Fallback to ITreeSitterImporter if direct import fails
                    }

                    // Fallback logic using ITreeSitterImporter
                    try {
                        const parserConstructor = await this._treeSitterImporter.getParserClass();
                        if (parserConstructor) {
                            // If getParserClass gives us the constructor, we might still need the full module for Language, etc.
                            // This part is a bit speculative as the original intent of getParserClass vs full module might differ.
                            // For now, assume if constructor is valid, we re-attempt import hoping environment is now good, or constructor is enough.
                             const fallbackFullModule = await import('@vscode/tree-sitter-wasm');
                             if (fallbackFullModule && fallbackFullModule.Parser && fallbackFullModule.Language) {
                                this.parserClass = fallbackFullModule;
                                this._logService.info('[TreeSitterParserLoaderImpl] Core parser module loaded via ITreeSitterImporter fallback path.');
                                return fallbackFullModule;
                             } else {
                                 this._logService.error('[TreeSitterParserLoaderImpl] Fallback import after getParserClass led to invalid/incomplete module.');
                                 return undefined;
                             }
                        } else {
                            this._logService.error('[TreeSitterParserLoaderImpl] Failed to get Parser constructor from ITreeSitterImporter.');
                            return undefined;
                        }
                    } catch (fallbackError) {
                        this._logService.error('[TreeSitterParserLoaderImpl] Error during ITreeSitterImporter fallback.', fallbackError);
                        return undefined;
                    }
                })();
            }
            return this._parserModulePromise;
        } else {
            this._logService.warn(`[TreeSitterParserLoaderImpl] loadParser called for non-core languageId: ${languageId}. This loader is primarily for the core parser.`);
            return Promise.resolve(undefined); // Or handle non-core languages if necessary
        }
    }
}