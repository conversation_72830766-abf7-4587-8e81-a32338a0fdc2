/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Coode AI Editor. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import type * as ParserWasmNamespace from '@vscode/tree-sitter-wasm';
import { URI } from '../../../../base/common/uri.js';
import { ILogService } from '../../../../platform/log/common/log.js';
import { ITreeSitterImporter } from '../treeSitterParserService.js'; // ITreeSitterImporter is in treeSitterParserService.ts
import { ITreeSitterParserLoader } from './treeSitterParserLoader.js';

export class TreeSitterParserLoaderImpl implements ITreeSitterParserLoader {
    readonly _serviceBrand: undefined;

    private _parserModulePromise: Promise<typeof ParserWasmNamespace | undefined> | undefined;
    public parserClass?: typeof ParserWasmNamespace;

    constructor(
        @ITreeSitterImporter private readonly _treeSitterImporter: ITreeSitterImporter,
        @ILogService private readonly _logService: ILogService
    ) {
        this._logService.trace('[TreeSitterParserLoaderImpl] constructor');
    }

    async loadParser(wasmUri: URI, languageId: string): Promise<typeof ParserWasmNamespace | undefined> {
        this._logService.info(`[TreeSitterParserLoaderImpl] loadParser called for languageId: ${languageId}, wasmUri: ${wasmUri.toString()}`);

        if (languageId === 'core') {
            if (!this._parserModulePromise) {
                this._parserModulePromise = (async () => {
                    try {
                        const parserConstructor = await this._treeSitterImporter.getParserClass();
                        if (parserConstructor) {
                            const fullModule = await import('@vscode/tree-sitter-wasm');
                            this.parserClass = fullModule;
                            this._logService.info('[TreeSitterParserLoaderImpl] Core parser module loaded and assigned via ITreeSitterImporter path.');
                            return fullModule;
                        } else {
                            this._logService.error('[TreeSitterParserLoaderImpl] Failed to get Parser constructor from ITreeSitterImporter. Cannot load core module.');
                            return undefined;
                        }
                    } catch (err) {
                        this._logService.error(`[TreeSitterParserLoaderImpl] Error in core parser module loading: ${err}`);
                        return undefined;
                    }
                })();
            }
            return this._parserModulePromise;
        } else {
            this._logService.warn(`[TreeSitterParserLoaderImpl] loadParser called for non-core languageId: ${languageId}. This loader is primarily for the core parser.`);
            return Promise.resolve(undefined);
        }
    }
}